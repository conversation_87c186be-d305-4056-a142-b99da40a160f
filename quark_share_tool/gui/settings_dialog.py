#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置对话框
用于配置程序运行参数和模式切换
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QWidget, QLabel, QRadioButton, QButtonGroup,
                            QSpinBox, QCheckBox, QLineEdit, QPushButton,
                            QGroupBox, QFormLayout, QDialogButtonBox,
                            QMessageBox, QTextEdit)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

from config import config
from core.api_factory import get_api_info, switch_mode

class SettingsDialog(QDialog):
    """设置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("程序设置")
        self.setFixedSize(600, 500)
        self.setModal(True)
        
        self.setup_ui()
        self.load_settings()
        
        # 应用样式
        self.apply_styles()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        
        # 运行模式选项卡
        self.create_mode_tab(tab_widget)
        
        # API配置选项卡
        self.create_api_tab(tab_widget)
        
        # 界面配置选项卡
        self.create_ui_tab(tab_widget)
        
        layout.addWidget(tab_widget)
        
        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | 
            QDialogButtonBox.StandardButton.Cancel |
            QDialogButtonBox.StandardButton.Apply
        )
        button_box.button(QDialogButtonBox.StandardButton.Ok).setText("确定")
        button_box.button(QDialogButtonBox.StandardButton.Cancel).setText("取消")
        button_box.button(QDialogButtonBox.StandardButton.Apply).setText("应用")
        
        button_box.accepted.connect(self.accept_settings)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.StandardButton.Apply).clicked.connect(self.apply_settings)
        
        layout.addWidget(button_box)
    
    def create_mode_tab(self, tab_widget):
        """创建运行模式选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 模式选择
        mode_group = QGroupBox("运行模式")
        mode_layout = QVBoxLayout(mode_group)
        
        self.mode_button_group = QButtonGroup()
        
        self.demo_radio = QRadioButton("🎭 演示模式")
        self.demo_radio.setToolTip("使用模拟数据，适合功能测试和演示")
        mode_layout.addWidget(self.demo_radio)
        self.mode_button_group.addButton(self.demo_radio, 0)

        self.web_radio = QRadioButton("🌐 网页端模式 (推荐)")
        self.web_radio.setToolTip("使用浏览器模拟操作，速度快，支持扫码登录")
        mode_layout.addWidget(self.web_radio)
        self.mode_button_group.addButton(self.web_radio, 1)

        self.api_radio = QRadioButton("🚀 原生API模式")
        self.api_radio.setToolTip("直接调用夸克网盘API接口")
        mode_layout.addWidget(self.api_radio)
        self.mode_button_group.addButton(self.api_radio, 2)
        
        layout.addWidget(mode_group)
        
        # 模式信息显示
        info_group = QGroupBox("模式信息")
        info_layout = QVBoxLayout(info_group)
        
        self.mode_info_text = QTextEdit()
        self.mode_info_text.setReadOnly(True)
        self.mode_info_text.setMaximumHeight(150)
        info_layout.addWidget(self.mode_info_text)
        
        layout.addWidget(info_group)
        
        # 绑定事件
        self.demo_radio.toggled.connect(self.update_mode_info)
        self.web_radio.toggled.connect(self.update_mode_info)
        self.api_radio.toggled.connect(self.update_mode_info)
        
        layout.addStretch()
        tab_widget.addTab(tab, "运行模式")
    
    def create_api_tab(self, tab_widget):
        """创建API配置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 分享配置
        share_group = QGroupBox("分享设置")
        share_layout = QFormLayout(share_group)
        
        self.expire_days_spin = QSpinBox()
        self.expire_days_spin.setRange(1, 365)
        self.expire_days_spin.setSuffix(" 天")
        share_layout.addRow("默认过期天数:", self.expire_days_spin)
        
        self.need_password_check = QCheckBox("默认需要提取码")
        share_layout.addRow("", self.need_password_check)
        
        self.batch_delay_spin = QSpinBox()
        self.batch_delay_spin.setRange(0, 10)
        self.batch_delay_spin.setSuffix(" 秒")
        share_layout.addRow("批量操作间隔:", self.batch_delay_spin)
        
        self.max_batch_spin = QSpinBox()
        self.max_batch_spin.setRange(1, 100)
        share_layout.addRow("最大批量数量:", self.max_batch_spin)
        
        layout.addWidget(share_group)
        
        # 网络配置
        network_group = QGroupBox("网络设置")
        network_layout = QFormLayout(network_group)
        
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(5, 120)
        self.timeout_spin.setSuffix(" 秒")
        network_layout.addRow("请求超时:", self.timeout_spin)
        
        self.retry_times_spin = QSpinBox()
        self.retry_times_spin.setRange(0, 10)
        network_layout.addRow("重试次数:", self.retry_times_spin)
        
        layout.addWidget(network_group)
        
        layout.addStretch()
        tab_widget.addTab(tab, "API配置")
    
    def create_ui_tab(self, tab_widget):
        """创建界面配置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 界面设置
        ui_group = QGroupBox("界面设置")
        ui_layout = QFormLayout(ui_group)
        
        self.window_size_edit = QLineEdit()
        self.window_size_edit.setPlaceholderText("例如: 1200x800")
        ui_layout.addRow("默认窗口大小:", self.window_size_edit)
        
        layout.addWidget(ui_group)
        
        # 导出设置
        export_group = QGroupBox("导出设置")
        export_layout = QFormLayout(export_group)
        
        self.include_stats_check = QCheckBox("包含统计信息")
        export_layout.addRow("", self.include_stats_check)
        
        self.auto_open_check = QCheckBox("导出后自动打开文件")
        export_layout.addRow("", self.auto_open_check)
        
        layout.addWidget(export_group)
        
        layout.addStretch()
        tab_widget.addTab(tab, "界面配置")
    
    def load_settings(self):
        """加载当前设置"""
        # 运行模式
        api_type = config.get("mode.api_type", "web")
        if config.is_demo_mode() or api_type == "demo":
            self.demo_radio.setChecked(True)
        elif api_type == "web":
            self.web_radio.setChecked(True)
        else:
            self.api_radio.setChecked(True)
        
        # API配置
        share_config = config.get_share_config()
        self.expire_days_spin.setValue(share_config.get("default_expire_days", 7))
        self.need_password_check.setChecked(share_config.get("default_need_password", True))
        self.batch_delay_spin.setValue(int(share_config.get("batch_delay", 0.5)))
        self.max_batch_spin.setValue(share_config.get("max_batch_size", 50))
        
        api_config = config.get_api_config()
        self.timeout_spin.setValue(api_config.get("timeout", 30))
        self.retry_times_spin.setValue(api_config.get("retry_times", 3))
        
        # 界面配置
        ui_config = config.get_ui_config()
        self.window_size_edit.setText(ui_config.get("window_size", "1200x800"))
        
        export_config = config.get("export", {})
        self.include_stats_check.setChecked(export_config.get("include_statistics", True))
        self.auto_open_check.setChecked(export_config.get("auto_open", False))
        
        # 更新模式信息
        self.update_mode_info()
    
    def update_mode_info(self):
        """更新模式信息显示"""
        # 临时设置API类型来获取信息
        old_api_type = config.get("mode.api_type", "web")
        old_demo_mode = config.is_demo_mode()

        if self.demo_radio.isChecked():
            config.set("mode.api_type", "demo")
            config.set_demo_mode(True)
        elif self.web_radio.isChecked():
            config.set("mode.api_type", "web")
            config.set_demo_mode(False)
        else:
            config.set("mode.api_type", "api")
            config.set_demo_mode(False)

        api_info = get_api_info()

        # 恢复原设置
        config.set("mode.api_type", old_api_type)
        config.set_demo_mode(old_demo_mode)
        
        info_text = f"📝 {api_info['description']}\n\n"
        
        info_text += "✨ 功能特性:\n"
        for feature in api_info.get('features', []):
            info_text += f"• {feature}\n"
        
        if 'limitations' in api_info:
            info_text += "\n⚠️ 使用限制:\n"
            for limitation in api_info['limitations']:
                info_text += f"• {limitation}\n"
        
        if 'requirements' in api_info:
            info_text += "\n📋 使用要求:\n"
            for requirement in api_info['requirements']:
                info_text += f"• {requirement}\n"
        
        self.mode_info_text.setPlainText(info_text)
    
    def apply_settings(self):
        """应用设置"""
        try:
            # 运行模式
            old_api_type = config.get("mode.api_type", "web")
            old_demo_mode = config.is_demo_mode()

            if self.demo_radio.isChecked():
                config.set("mode.api_type", "demo")
                config.set_demo_mode(True)
                mode_name = "演示模式"
            elif self.web_radio.isChecked():
                config.set("mode.api_type", "web")
                config.set_demo_mode(False)
                mode_name = "网页端模式"
            else:
                config.set("mode.api_type", "api")
                config.set_demo_mode(False)
                mode_name = "原生API模式"

            # 检查是否有变化
            new_api_type = config.get("mode.api_type", "web")
            new_demo_mode = config.is_demo_mode()

            if old_api_type != new_api_type or old_demo_mode != new_demo_mode:
                QMessageBox.information(self, "模式切换", f"已切换到{mode_name}，重启程序后生效")
            
            # API配置
            config.set("share.default_expire_days", self.expire_days_spin.value())
            config.set("share.default_need_password", self.need_password_check.isChecked())
            config.set("share.batch_delay", self.batch_delay_spin.value())
            config.set("share.max_batch_size", self.max_batch_spin.value())
            
            config.set("api.timeout", self.timeout_spin.value())
            config.set("api.retry_times", self.retry_times_spin.value())
            
            # 界面配置
            config.set("ui.window_size", self.window_size_edit.text())
            config.set("export.include_statistics", self.include_stats_check.isChecked())
            config.set("export.auto_open", self.auto_open_check.isChecked())
            
            # 保存配置
            config.save_user_config()
            
            QMessageBox.information(self, "成功", "设置已保存")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存设置失败: {e}")
    
    def accept_settings(self):
        """确定并关闭"""
        self.apply_settings()
        self.accept()
    
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #ccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QRadioButton {
                font-size: 12px;
                padding: 5px;
            }
            QCheckBox {
                font-size: 12px;
                padding: 5px;
            }
            QTextEdit {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px;
                font-size: 11px;
            }
        """)
