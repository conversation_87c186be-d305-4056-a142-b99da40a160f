#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化主窗口GUI
更加图形化和用户友好的界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
from datetime import datetime

from core.quark_api import QuarkAPI
from utils.excel_exporter import ExcelExporter
from utils.logger import setup_logger
from utils.icons import icon_manager
from utils.themes import theme_manager, ModernButton
from gui.login_dialog import LoginDialog
from gui.progress_dialog import ProgressDialog

class ModernMainWindow:
    """现代化主窗口类"""
    
    def __init__(self, root):
        self.root = root
        self.logger = setup_logger()
        self.quark_api = QuarkAPI()
        self.excel_exporter = ExcelExporter()
        self.share_results = []
        self.selected_files = set()
        
        self.setup_window()
        self.setup_ui()
        self.setup_shortcuts()
        self.center_window()

        # 应用主题
        theme_manager.configure_ttk_style(self.root)
    
    def setup_window(self):
        """设置窗口属性"""
        self.root.title("🌟 夸克网盘批量分享工具 v2.0")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        self.root.configure(bg=theme_manager.get_color("bg"))
        
        # 设置窗口图标（使用Unicode字符）
        try:
            # 在某些系统上可能不支持
            self.root.iconname("夸克分享工具")
        except:
            pass
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建菜单栏
        self.create_menu_bar()

        # 创建主容器
        main_container = tk.Frame(self.root, bg=theme_manager.get_color("bg"))
        main_container.pack(fill="both", expand=True, padx=10, pady=10)

        # 创建顶部工具栏
        self.create_toolbar(main_container)

        # 创建主要内容区域
        self.create_main_content(main_container)

        # 创建底部状态栏
        self.create_status_bar(main_container)
    
    def create_toolbar(self, parent):
        """创建工具栏"""
        toolbar_frame = tk.Frame(parent, bg=theme_manager.get_color("frame_bg"), 
                                relief="solid", bd=1)
        toolbar_frame.pack(fill="x", pady=(0, 10))
        
        # 左侧：登录区域
        login_frame = tk.Frame(toolbar_frame, bg=theme_manager.get_color("frame_bg"))
        login_frame.pack(side="left", padx=10, pady=8)
        
        # 登录状态指示器
        self.login_indicator = tk.Label(login_frame, text="●", font=("", 12), 
                                       fg=theme_manager.get_color("error"),
                                       bg=theme_manager.get_color("frame_bg"))
        self.login_indicator.pack(side="left", padx=(0, 5))
        
        self.login_status_label = tk.Label(login_frame, text="未登录", 
                                          font=("", 9, "bold"),
                                          fg=theme_manager.get_color("error"),
                                          bg=theme_manager.get_color("frame_bg"))
        self.login_status_label.pack(side="left", padx=(0, 10))
        
        # 登录/登出按钮
        self.login_button = ModernButton(login_frame, text="🔑 登录", 
                                        command=self.show_login_dialog,
                                        icon=icon_manager.get_icon('login'))
        self.login_button.pack(side="left", padx=(0, 5))
        
        self.logout_button = ModernButton(login_frame, text="🚪 登出", 
                                         command=self.logout, style="warning",
                                         icon=icon_manager.get_icon('logout'),
                                         state="disabled")
        self.logout_button.pack(side="left")
        
        # 右侧：主题切换
        theme_frame = tk.Frame(toolbar_frame, bg=theme_manager.get_color("frame_bg"))
        theme_frame.pack(side="right", padx=10, pady=8)
        
        tk.Label(theme_frame, text="主题:", font=("", 9),
                bg=theme_manager.get_color("frame_bg"),
                fg=theme_manager.get_color("fg")).pack(side="left", padx=(0, 5))
        
        self.theme_var = tk.StringVar(value="浅色")
        theme_combo = ttk.Combobox(theme_frame, textvariable=self.theme_var,
                                  values=["浅色", "深色"], state="readonly", width=8)
        theme_combo.pack(side="left")
        theme_combo.bind("<<ComboboxSelected>>", self.change_theme)
    
    def create_main_content(self, parent):
        """创建主要内容区域"""
        content_frame = tk.Frame(parent, bg=theme_manager.get_color("bg"))
        content_frame.pack(fill="both", expand=True)
        
        # 创建左右分栏
        paned_window = ttk.PanedWindow(content_frame, orient="horizontal")
        paned_window.pack(fill="both", expand=True)
        
        # 左侧：文件管理区域
        self.create_file_management_area(paned_window)
        
        # 右侧：分享结果区域
        self.create_share_results_area(paned_window)
    
    def create_file_management_area(self, parent):
        """创建文件管理区域"""
        file_frame = ttk.LabelFrame(parent, text="📁 文件管理", style="Modern.TLabelframe")
        parent.add(file_frame, weight=3)
        
        # 操作按钮区域
        button_frame = tk.Frame(file_frame, bg=theme_manager.get_color("frame_bg"))
        button_frame.pack(fill="x", padx=10, pady=(10, 5))
        
        # 第一行按钮
        button_row1 = tk.Frame(button_frame, bg=theme_manager.get_color("frame_bg"))
        button_row1.pack(fill="x", pady=(0, 5))
        
        self.refresh_button = ModernButton(button_row1, text="🔄 刷新列表", 
                                          command=self.refresh_file_list,
                                          icon=icon_manager.get_icon('refresh'),
                                          state="disabled")
        self.refresh_button.pack(side="left", padx=(0, 5))
        
        self.select_all_button = ModernButton(button_row1, text="☑️ 全选", 
                                             command=self.select_all_files,
                                             icon=icon_manager.get_icon('select_all'),
                                             state="disabled")
        self.select_all_button.pack(side="left", padx=(0, 5))
        
        self.deselect_all_button = ModernButton(button_row1, text="☐ 取消全选", 
                                               command=self.deselect_all_files,
                                               icon=icon_manager.get_icon('deselect_all'),
                                               state="disabled")
        self.deselect_all_button.pack(side="left")
        
        # 第二行按钮
        button_row2 = tk.Frame(button_frame, bg=theme_manager.get_color("frame_bg"))
        button_row2.pack(fill="x")
        
        self.batch_share_button = ModernButton(button_row2, text="🚀 批量分享", 
                                              command=self.batch_share, style="success",
                                              icon=icon_manager.get_icon('share'),
                                              state="disabled")
        self.batch_share_button.pack(side="left", padx=(0, 5))
        
        # 文件统计标签
        self.file_stats_label = tk.Label(button_row2, text="文件: 0 | 已选: 0", 
                                        font=("", 9),
                                        bg=theme_manager.get_color("frame_bg"),
                                        fg=theme_manager.get_color("fg"))
        self.file_stats_label.pack(side="right")
        
        # 搜索框
        search_frame = tk.Frame(file_frame, bg=theme_manager.get_color("frame_bg"))
        search_frame.pack(fill="x", padx=10, pady=(0, 5))
        
        tk.Label(search_frame, text="🔍 搜索:", font=("", 9),
                bg=theme_manager.get_color("frame_bg"),
                fg=theme_manager.get_color("fg")).pack(side="left", padx=(0, 5))
        
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var, 
                                     style="Modern.TEntry")
        self.search_entry.pack(side="left", fill="x", expand=True, padx=(0, 5))
        self.search_var.trace("w", self.filter_files)
        
        clear_search_btn = ModernButton(search_frame, text="✖", command=self.clear_search)
        clear_search_btn.pack(side="left")
        
        # 文件列表
        list_frame = tk.Frame(file_frame, bg=theme_manager.get_color("bg"))
        list_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        # 创建Treeview
        columns = ("选择", "图标", "文件名", "大小", "类型", "修改时间")
        self.file_tree = ttk.Treeview(list_frame, columns=columns, show="headings", 
                                     style="Modern.Treeview", height=15)
        
        # 设置列标题和宽度
        column_configs = {
            "选择": (50, "center"),
            "图标": (40, "center"),
            "文件名": (250, "w"),
            "大小": (80, "e"),
            "类型": (80, "center"),
            "修改时间": (120, "center")
        }
        
        for col, (width, anchor) in column_configs.items():
            self.file_tree.heading(col, text=col, command=lambda c=col: self.sort_files(c))
            self.file_tree.column(col, width=width, anchor=anchor)
        
        # 添加滚动条
        file_scrollbar_v = ttk.Scrollbar(list_frame, orient="vertical", 
                                        command=self.file_tree.yview)
        file_scrollbar_h = ttk.Scrollbar(list_frame, orient="horizontal", 
                                        command=self.file_tree.xview)
        self.file_tree.configure(yscrollcommand=file_scrollbar_v.set,
                                xscrollcommand=file_scrollbar_h.set)
        
        # 布局
        self.file_tree.grid(row=0, column=0, sticky="nsew")
        file_scrollbar_v.grid(row=0, column=1, sticky="ns")
        file_scrollbar_h.grid(row=1, column=0, sticky="ew")
        
        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定事件
        self.file_tree.bind("<Double-1>", self.toggle_file_selection)
        self.file_tree.bind("<Button-3>", self.show_context_menu)  # 右键菜单
    
    def create_share_results_area(self, parent):
        """创建分享结果区域"""
        result_frame = ttk.LabelFrame(parent, text="📊 分享结果", style="Modern.TLabelframe")
        parent.add(result_frame, weight=2)
        
        # 操作按钮
        button_frame = tk.Frame(result_frame, bg=theme_manager.get_color("frame_bg"))
        button_frame.pack(fill="x", padx=10, pady=(10, 5))
        
        self.export_button = ModernButton(button_frame, text="📄 导出Excel", 
                                         command=self.export_to_excel, style="success",
                                         icon=icon_manager.get_icon('excel'),
                                         state="disabled")
        self.export_button.pack(side="left", padx=(0, 5))
        
        self.clear_results_button = ModernButton(button_frame, text="🗑️ 清空结果", 
                                                command=self.clear_results, style="warning")
        self.clear_results_button.pack(side="left")
        
        # 结果统计
        self.result_stats_label = tk.Label(button_frame, text="结果: 0 | 成功: 0 | 失败: 0", 
                                          font=("", 9),
                                          bg=theme_manager.get_color("frame_bg"),
                                          fg=theme_manager.get_color("fg"))
        self.result_stats_label.pack(side="right")
        
        # 结果列表
        result_list_frame = tk.Frame(result_frame, bg=theme_manager.get_color("bg"))
        result_list_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        # 创建结果Treeview
        result_columns = ("状态", "文件名", "分享链接", "提取码", "创建时间")
        self.result_tree = ttk.Treeview(result_list_frame, columns=result_columns, 
                                       show="headings", style="Modern.Treeview", height=15)
        
        # 设置结果列
        result_column_configs = {
            "状态": (60, "center"),
            "文件名": (150, "w"),
            "分享链接": (200, "w"),
            "提取码": (80, "center"),
            "创建时间": (120, "center")
        }
        
        for col, (width, anchor) in result_column_configs.items():
            self.result_tree.heading(col, text=col)
            self.result_tree.column(col, width=width, anchor=anchor)
        
        # 添加滚动条
        result_scrollbar_v = ttk.Scrollbar(result_list_frame, orient="vertical", 
                                          command=self.result_tree.yview)
        result_scrollbar_h = ttk.Scrollbar(result_list_frame, orient="horizontal", 
                                          command=self.result_tree.xview)
        self.result_tree.configure(yscrollcommand=result_scrollbar_v.set,
                                  xscrollcommand=result_scrollbar_h.set)
        
        # 布局
        self.result_tree.grid(row=0, column=0, sticky="nsew")
        result_scrollbar_v.grid(row=0, column=1, sticky="ns")
        result_scrollbar_h.grid(row=1, column=0, sticky="ew")
        
        result_list_frame.grid_rowconfigure(0, weight=1)
        result_list_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定双击复制链接
        self.result_tree.bind("<Double-1>", self.copy_share_link)

    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = tk.Frame(parent, bg=theme_manager.get_color("frame_bg"),
                               relief="solid", bd=1)
        status_frame.pack(fill="x", pady=(10, 0))

        # 状态文本
        self.status_var = tk.StringVar()
        self.status_var.set("🟢 就绪")

        self.status_label = tk.Label(status_frame, textvariable=self.status_var,
                                    font=("", 9), anchor="w",
                                    bg=theme_manager.get_color("frame_bg"),
                                    fg=theme_manager.get_color("fg"))
        self.status_label.pack(side="left", padx=10, pady=5)

        # 进度条（隐藏状态）
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(status_frame, variable=self.progress_var,
                                           style="Modern.TProgressbar", length=200)

        # 时间显示
        self.time_label = tk.Label(status_frame, font=("", 9),
                                  bg=theme_manager.get_color("frame_bg"),
                                  fg=theme_manager.get_color("fg"))
        self.time_label.pack(side="right", padx=10, pady=5)

        # 更新时间
        self.update_time()

    def center_window(self):
        """窗口居中"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=f"⏰ {current_time}")
        self.root.after(1000, self.update_time)

    def change_theme(self, event=None):
        """切换主题"""
        theme_name = "light" if self.theme_var.get() == "浅色" else "dark"
        theme_manager.set_theme(theme_name)

        # 重新应用主题
        theme_manager.configure_ttk_style(self.root)
        self.apply_theme_to_all_widgets()

        self.status_var.set(f"🎨 已切换到{self.theme_var.get()}主题")

    def apply_theme_to_all_widgets(self):
        """为所有控件应用主题"""
        def apply_to_widget(widget):
            try:
                if isinstance(widget, tk.Frame):
                    widget.configure(bg=theme_manager.get_color("frame_bg"))
                elif isinstance(widget, tk.Label):
                    widget.configure(bg=theme_manager.get_color("frame_bg"),
                                   fg=theme_manager.get_color("fg"))
                elif isinstance(widget, ModernButton):
                    # ModernButton会自动处理主题
                    pass

                # 递归处理子控件
                for child in widget.winfo_children():
                    apply_to_widget(child)
            except:
                pass

        apply_to_widget(self.root)

    def show_login_dialog(self):
        """显示登录对话框"""
        dialog = LoginDialog(self.root)
        if dialog.result:
            username, password = dialog.result
            self.login(username, password)

    def login(self, username, password):
        """登录"""
        try:
            self.logger.info(f"尝试登录用户: {username}")
            self.status_var.set("🔄 正在登录...")

            if self.quark_api.login(username, password):
                # 更新UI状态
                self.login_indicator.config(fg=theme_manager.get_color("success"))
                self.login_status_label.config(text=f"已登录: {username}",
                                             fg=theme_manager.get_color("success"))
                self.login_button.config(state="disabled")
                self.logout_button.config(state="normal")
                self.refresh_button.config(state="normal")

                self.status_var.set("✅ 登录成功")
                self.logger.info(f"用户 {username} 登录成功")

                # 自动刷新文件列表
                self.refresh_file_list()
            else:
                self.logger.warning(f"用户 {username} 登录失败：用户名或密码错误")
                messagebox.showerror("登录失败", "用户名或密码错误")
                self.status_var.set("❌ 登录失败")

        except Exception as e:
            self.logger.error(f"登录时发生错误: {e}")
            messagebox.showerror("登录错误", f"登录时发生错误: {e}")
            self.status_var.set("❌ 登录错误")

    def logout(self):
        """登出"""
        self.quark_api.logout()

        # 更新UI状态
        self.login_indicator.config(fg=theme_manager.get_color("error"))
        self.login_status_label.config(text="未登录",
                                     fg=theme_manager.get_color("error"))
        self.login_button.config(state="normal")
        self.logout_button.config(state="disabled")
        self.refresh_button.config(state="disabled")
        self.select_all_button.config(state="disabled")
        self.deselect_all_button.config(state="disabled")
        self.batch_share_button.config(state="disabled")

        # 清空文件列表
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

        self.selected_files.clear()
        self.update_file_stats()

        self.status_var.set("🚪 已登出")

    def refresh_file_list(self):
        """刷新文件列表"""
        try:
            self.status_var.set("🔄 正在获取文件列表...")

            # 清空现有列表
            for item in self.file_tree.get_children():
                self.file_tree.delete(item)
            self.selected_files.clear()

            # 获取文件列表
            files = self.quark_api.get_files()

            # 添加到树形控件
            for file_obj in files:
                size_str = self._format_file_size(file_obj.size) if file_obj.file_type == "file" else "-"
                type_str = "📁 文件夹" if file_obj.file_type == "folder" else "📄 文件"
                icon = "📁" if file_obj.file_type == "folder" else "📄"

                # 格式化修改时间
                mod_time = file_obj.created_time.strftime("%m-%d %H:%M")

                item_id = self.file_tree.insert("", "end", values=(
                    "☐", icon, file_obj.name, size_str, type_str, mod_time
                ), tags=(file_obj.file_id,))

                # 根据文件类型设置不同颜色
                if file_obj.file_type == "folder":
                    self.file_tree.set(item_id, "图标", "📁")
                else:
                    self.file_tree.set(item_id, "图标", "📄")

            # 启用操作按钮
            self.select_all_button.config(state="normal")
            self.deselect_all_button.config(state="normal")
            self.batch_share_button.config(state="normal")

            self.update_file_stats()
            self.status_var.set(f"✅ 已加载 {len(files)} 个文件")

        except Exception as e:
            messagebox.showerror("错误", f"获取文件列表失败: {e}")
            self.status_var.set("❌ 获取文件列表失败")

    def _format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)

        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1

        return f"{size:.1f} {size_names[i]}"

    def update_file_stats(self):
        """更新文件统计信息"""
        total_files = len(self.file_tree.get_children())
        selected_count = len(self.selected_files)
        self.file_stats_label.config(text=f"文件: {total_files} | 已选: {selected_count}")

    def toggle_file_selection(self, event=None):
        """切换文件选择状态"""
        try:
            selection = self.file_tree.selection()
            if not selection:
                return

            item = selection[0]
            file_id = self.file_tree.item(item, "tags")[0]
            current_values = list(self.file_tree.item(item, "values"))

            # 切换选择状态
            if current_values[0] == "☐":
                current_values[0] = "☑"
                self.selected_files.add(file_id)
            else:
                current_values[0] = "☐"
                self.selected_files.discard(file_id)

            self.file_tree.item(item, values=current_values)
            self.update_file_stats()
            self.logger.debug(f"切换文件选择状态: {current_values[2]}")

        except Exception as e:
            self.logger.error(f"切换文件选择状态失败: {e}")
            messagebox.showerror("错误", f"操作失败: {e}")

    def select_all_files(self):
        """全选文件"""
        try:
            for item in self.file_tree.get_children():
                file_id = self.file_tree.item(item, "tags")[0]
                current_values = list(self.file_tree.item(item, "values"))
                current_values[0] = "☑"
                self.file_tree.item(item, values=current_values)
                self.selected_files.add(file_id)

            self.update_file_stats()
            self.status_var.set("✅ 已全选所有文件")

        except Exception as e:
            self.logger.error(f"全选文件失败: {e}")
            messagebox.showerror("错误", f"全选失败: {e}")

    def deselect_all_files(self):
        """取消全选文件"""
        try:
            for item in self.file_tree.get_children():
                current_values = list(self.file_tree.item(item, "values"))
                current_values[0] = "☐"
                self.file_tree.item(item, values=current_values)

            self.selected_files.clear()
            self.update_file_stats()
            self.status_var.set("✅ 已取消选择所有文件")

        except Exception as e:
            self.logger.error(f"取消全选失败: {e}")
            messagebox.showerror("错误", f"取消全选失败: {e}")

    def filter_files(self, *args):
        """过滤文件"""
        search_text = self.search_var.get().lower()

        for item in self.file_tree.get_children():
            values = self.file_tree.item(item, "values")
            file_name = values[2].lower()  # 文件名列

            if search_text in file_name:
                self.file_tree.reattach(item, "", "end")
            else:
                self.file_tree.detach(item)

    def clear_search(self):
        """清空搜索"""
        self.search_var.set("")
        # 重新显示所有文件
        for item in self.file_tree.get_children():
            self.file_tree.reattach(item, "", "end")

    def sort_files(self, column):
        """排序文件"""
        # 这里可以实现文件排序功能
        self.status_var.set(f"📊 按{column}排序")

    def show_context_menu(self, event):
        """显示右键菜单"""
        try:
            # 创建右键菜单
            context_menu = tk.Menu(self.root, tearoff=0)
            context_menu.add_command(label="🔄 切换选择", command=self.toggle_file_selection)
            context_menu.add_separator()
            context_menu.add_command(label="☑️ 全选", command=self.select_all_files)
            context_menu.add_command(label="☐ 取消全选", command=self.deselect_all_files)
            context_menu.add_separator()
            context_menu.add_command(label="🚀 批量分享", command=self.batch_share)

            # 显示菜单
            context_menu.post(event.x_root, event.y_root)

        except Exception as e:
            self.logger.error(f"显示右键菜单失败: {e}")

    def batch_share(self):
        """批量分享"""
        if not self.selected_files:
            messagebox.showwarning("警告", "请先选择要分享的文件")
            return

        selected_list = list(self.selected_files)

        # 显示进度对话框并开始分享
        progress_dialog = ProgressDialog(self.root, "🚀 批量分享进度", len(selected_list))

        def share_worker():
            try:
                self.share_results = self.quark_api.batch_create_share_links(
                    selected_list,
                    expire_days=7,
                    need_password=True,
                    progress_callback=progress_dialog.update_progress
                )

                # 在主线程中更新UI
                self.root.after(0, self.update_result_display)
                self.root.after(0, progress_dialog.close)

            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"批量分享失败: {e}"))
                self.root.after(0, progress_dialog.close)

        # 启动工作线程
        thread = threading.Thread(target=share_worker)
        thread.daemon = True
        thread.start()

        self.status_var.set(f"🚀 正在分享 {len(selected_list)} 个文件...")

    def update_result_display(self):
        """更新结果显示"""
        # 清空现有结果
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)

        # 添加新结果
        for result in self.share_results:
            status_icon = "✅" if result["status"] == "成功" else "❌"
            short_link = result["share_link"][:30] + "..." if len(result["share_link"]) > 30 else result["share_link"]

            item_id = self.result_tree.insert("", "end", values=(
                status_icon,
                result["file_name"],
                short_link,
                result["share_code"],
                result["created_time"]
            ))

            # 根据状态设置行颜色
            if result["status"] == "成功":
                self.result_tree.set(item_id, "状态", "✅")
            else:
                self.result_tree.set(item_id, "状态", "❌")

        # 启用导出按钮
        if self.share_results:
            self.export_button.config(state="normal")

        # 更新统计信息
        self.update_result_stats()

        # 更新状态
        success_count = sum(1 for r in self.share_results if r["status"] == "成功")
        total_count = len(self.share_results)
        self.status_var.set(f"✅ 分享完成: {success_count}/{total_count} 成功")

    def update_result_stats(self):
        """更新结果统计信息"""
        total = len(self.share_results)
        success = sum(1 for r in self.share_results if r["status"] == "成功")
        failed = total - success
        self.result_stats_label.config(text=f"结果: {total} | 成功: {success} | 失败: {failed}")

    def copy_share_link(self, event=None):
        """复制分享链接"""
        try:
            selection = self.result_tree.selection()
            if not selection:
                return

            item = selection[0]
            values = self.result_tree.item(item, "values")
            share_link = values[2]  # 分享链接列

            # 找到完整链接
            file_name = values[1]
            full_link = ""
            for result in self.share_results:
                if result["file_name"] == file_name:
                    full_link = result["share_link"]
                    break

            if full_link:
                self.root.clipboard_clear()
                self.root.clipboard_append(full_link)
                self.status_var.set(f"📋 已复制分享链接: {file_name}")

        except Exception as e:
            self.logger.error(f"复制分享链接失败: {e}")
            messagebox.showerror("错误", f"复制失败: {e}")

    def clear_results(self):
        """清空结果"""
        if messagebox.askyesno("确认", "确定要清空所有分享结果吗？"):
            for item in self.result_tree.get_children():
                self.result_tree.delete(item)

            self.share_results.clear()
            self.export_button.config(state="disabled")
            self.update_result_stats()
            self.status_var.set("🗑️ 已清空分享结果")

    def export_to_excel(self):
        """导出到Excel"""
        if not self.share_results:
            messagebox.showwarning("警告", "没有分享结果可导出")
            return

        # 选择保存路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"夸克网盘分享记录_{timestamp}.xlsx"

        file_path = filedialog.asksaveasfilename(
            title="保存Excel文件",
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")],
            initialvalue=default_filename
        )

        if file_path:
            try:
                self.status_var.set("📄 正在导出Excel...")

                # 显示进度
                self.progress_bar.pack(side="left", padx=(10, 0))
                self.progress_var.set(50)
                self.root.update()

                output_path = self.excel_exporter.export_share_results(self.share_results, file_path)

                self.progress_var.set(100)
                self.root.update()

                messagebox.showinfo("成功", f"Excel文件已导出到:\n{output_path}")
                self.status_var.set("✅ Excel导出成功")

                # 隐藏进度条
                self.progress_bar.pack_forget()
                self.progress_var.set(0)

            except Exception as e:
                self.progress_bar.pack_forget()
                self.progress_var.set(0)
                messagebox.showerror("错误", f"导出Excel失败: {e}")
                self.status_var.set("❌ Excel导出失败")

    def setup_shortcuts(self):
        """设置快捷键"""
        # 绑定快捷键
        self.root.bind("<Control-l>", lambda e: self.show_login_dialog())  # Ctrl+L 登录
        self.root.bind("<Control-r>", lambda e: self.refresh_file_list())  # Ctrl+R 刷新
        self.root.bind("<Control-a>", lambda e: self.select_all_files())   # Ctrl+A 全选
        self.root.bind("<Control-d>", lambda e: self.deselect_all_files()) # Ctrl+D 取消全选
        self.root.bind("<Control-s>", lambda e: self.batch_share())        # Ctrl+S 分享
        self.root.bind("<Control-e>", lambda e: self.export_to_excel())    # Ctrl+E 导出
        self.root.bind("<F5>", lambda e: self.refresh_file_list())         # F5 刷新
        self.root.bind("<Delete>", lambda e: self.clear_results())         # Delete 清空结果
        self.root.bind("<Escape>", lambda e: self.clear_search())          # Esc 清空搜索

        # 绑定文件列表快捷键
        self.file_tree.bind("<Return>", self.toggle_file_selection)        # 回车切换选择
        self.file_tree.bind("<space>", self.toggle_file_selection)         # 空格切换选择

        # 绑定结果列表快捷键
        self.result_tree.bind("<Control-c>", self.copy_share_link)         # Ctrl+C 复制链接

        # 搜索框快捷键
        self.search_entry.bind("<Return>", lambda e: self.search_entry.focus_set())
        self.search_entry.bind("<Escape>", lambda e: self.clear_search())

    def show_shortcuts_help(self):
        """显示快捷键帮助"""
        help_text = """
🎯 快捷键说明

📋 通用操作:
• Ctrl+L    登录/登出
• Ctrl+R    刷新文件列表
• F5        刷新文件列表
• Esc       清空搜索

📁 文件操作:
• Ctrl+A    全选文件
• Ctrl+D    取消全选
• 回车/空格  切换文件选择
• 双击      切换文件选择

🚀 分享操作:
• Ctrl+S    批量分享
• Ctrl+E    导出Excel
• Delete    清空分享结果

📊 结果操作:
• Ctrl+C    复制分享链接
• 双击      复制分享链接

💡 提示: 右键点击文件可显示快捷菜单
        """

        messagebox.showinfo("快捷键帮助", help_text)

    def show_about(self):
        """显示关于信息"""
        about_text = """
🌟 夸克网盘批量分享工具 v2.0

✨ 主要功能:
• 批量创建分享链接
• Excel格式导出
• 现代化图形界面
• 主题切换支持
• 快捷键操作

🛠️ 技术栈:
• Python 3.7+
• tkinter GUI
• openpyxl Excel处理
• PIL 图像处理

📧 联系方式:
如有问题或建议，请提交Issue

© 2024 夸克网盘批量分享工具
        """

        messagebox.showinfo("关于", about_text)

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="🔑 登录", command=self.show_login_dialog, accelerator="Ctrl+L")
        file_menu.add_command(label="🚪 登出", command=self.logout)
        file_menu.add_separator()
        file_menu.add_command(label="🔄 刷新", command=self.refresh_file_list, accelerator="Ctrl+R")
        file_menu.add_separator()
        file_menu.add_command(label="❌ 退出", command=self.root.quit, accelerator="Alt+F4")

        # 编辑菜单
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="☑️ 全选", command=self.select_all_files, accelerator="Ctrl+A")
        edit_menu.add_command(label="☐ 取消全选", command=self.deselect_all_files, accelerator="Ctrl+D")
        edit_menu.add_separator()
        edit_menu.add_command(label="🔍 清空搜索", command=self.clear_search, accelerator="Esc")

        # 操作菜单
        action_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="操作", menu=action_menu)
        action_menu.add_command(label="🚀 批量分享", command=self.batch_share, accelerator="Ctrl+S")
        action_menu.add_command(label="📄 导出Excel", command=self.export_to_excel, accelerator="Ctrl+E")
        action_menu.add_separator()
        action_menu.add_command(label="🗑️ 清空结果", command=self.clear_results, accelerator="Delete")

        # 视图菜单
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="视图", menu=view_menu)

        # 主题子菜单
        theme_submenu = tk.Menu(view_menu, tearoff=0)
        view_menu.add_cascade(label="🎨 主题", menu=theme_submenu)
        theme_submenu.add_command(label="☀️ 浅色主题", command=lambda: self.set_theme_by_name("浅色"))
        theme_submenu.add_command(label="🌙 深色主题", command=lambda: self.set_theme_by_name("深色"))

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="⌨️ 快捷键", command=self.show_shortcuts_help)
        help_menu.add_command(label="ℹ️ 关于", command=self.show_about)

    def set_theme_by_name(self, theme_name):
        """通过名称设置主题"""
        self.theme_var.set(theme_name)
        self.change_theme()
