#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版主窗口GUI - 确保登录功能正常工作
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
from datetime import datetime

from core.quark_api import QuarkAPI
from utils.excel_exporter import ExcelExporter
from utils.logger import setup_logger
from gui.login_dialog import LoginDialog
from gui.progress_dialog import ProgressDialog

class SimpleMainWindow:
    """简化版主窗口类"""
    
    def __init__(self, root):
        self.root = root
        self.logger = setup_logger()
        self.quark_api = QuarkAPI()
        self.excel_exporter = ExcelExporter()
        self.share_results = []
        self.selected_files = set()
        
        self.setup_window()
        self.setup_ui()
        self.center_window()
    
    def setup_window(self):
        """设置窗口属性"""
        self.root.title("🌟 夸克网盘批量分享工具 v2.0 (简化版)")
        self.root.geometry("1000x700")
        self.root.minsize(800, 500)
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主容器
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill="both", expand=True)
        
        # 登录区域
        self.create_login_frame(main_frame)
        
        # 操作区域
        self.create_operation_frame(main_frame)
        
        # 文件列表区域
        self.create_file_list_frame(main_frame)
        
        # 结果区域
        self.create_result_frame(main_frame)
        
        # 状态栏
        self.create_status_bar()
    
    def create_login_frame(self, parent):
        """创建登录区域"""
        login_frame = ttk.LabelFrame(parent, text="账户登录", padding="5")
        login_frame.pack(fill="x", pady=(0, 10))
        
        # 登录状态标签
        self.login_status_label = ttk.Label(login_frame, text="未登录", foreground="red")
        self.login_status_label.pack(side="left", padx=(0, 10))
        
        # 登录按钮
        self.login_button = ttk.Button(login_frame, text="🔑 登录", command=self.show_login_dialog)
        self.login_button.pack(side="left", padx=(0, 10))
        
        # 登出按钮
        self.logout_button = ttk.Button(login_frame, text="🚪 登出", command=self.logout, state="disabled")
        self.logout_button.pack(side="left")
    
    def create_operation_frame(self, parent):
        """创建操作区域"""
        op_frame = ttk.LabelFrame(parent, text="批量操作", padding="5")
        op_frame.pack(fill="x", pady=(0, 10))
        
        # 刷新文件列表按钮
        self.refresh_button = ttk.Button(op_frame, text="🔄 刷新文件列表", 
                                       command=self.refresh_file_list, state="disabled")
        self.refresh_button.pack(side="left", padx=(0, 10))
        
        # 全选按钮
        self.select_all_button = ttk.Button(op_frame, text="☑️ 全选", 
                                          command=self.select_all_files, state="disabled")
        self.select_all_button.pack(side="left", padx=(0, 10))
        
        # 取消全选按钮
        self.deselect_all_button = ttk.Button(op_frame, text="☐ 取消全选", 
                                            command=self.deselect_all_files, state="disabled")
        self.deselect_all_button.pack(side="left", padx=(0, 10))
        
        # 批量分享按钮
        self.batch_share_button = ttk.Button(op_frame, text="🚀 批量分享", 
                                           command=self.batch_share, state="disabled")
        self.batch_share_button.pack(side="left", padx=(0, 10))
        
        # 导出Excel按钮
        self.export_button = ttk.Button(op_frame, text="📄 导出Excel", 
                                      command=self.export_to_excel, state="disabled")
        self.export_button.pack(side="left")
    
    def create_file_list_frame(self, parent):
        """创建文件列表区域"""
        file_frame = ttk.LabelFrame(parent, text="文件列表", padding="5")
        file_frame.pack(side="left", fill="both", expand=True, padx=(0, 5))
        
        # 创建Treeview
        columns = ("选择", "文件名", "大小", "类型")
        self.file_tree = ttk.Treeview(file_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题
        for col in columns:
            self.file_tree.heading(col, text=col)
        
        # 设置列宽
        self.file_tree.column("选择", width=50, anchor="center")
        self.file_tree.column("文件名", width=250, anchor="w")
        self.file_tree.column("大小", width=80, anchor="e")
        self.file_tree.column("类型", width=80, anchor="center")
        
        # 添加滚动条
        file_scrollbar = ttk.Scrollbar(file_frame, orient="vertical", command=self.file_tree.yview)
        self.file_tree.configure(yscrollcommand=file_scrollbar.set)
        
        # 布局
        self.file_tree.pack(side="left", fill="both", expand=True)
        file_scrollbar.pack(side="right", fill="y")
        
        # 绑定双击事件
        self.file_tree.bind("<Double-1>", self.toggle_file_selection)
    
    def create_result_frame(self, parent):
        """创建结果区域"""
        result_frame = ttk.LabelFrame(parent, text="分享结果", padding="5")
        result_frame.pack(side="right", fill="both", expand=True, padx=(5, 0))
        
        # 创建Treeview
        columns = ("文件名", "分享链接", "提取码", "状态")
        self.result_tree = ttk.Treeview(result_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题
        for col in columns:
            self.result_tree.heading(col, text=col)
        
        # 设置列宽
        self.result_tree.column("文件名", width=120, anchor="w")
        self.result_tree.column("分享链接", width=150, anchor="w")
        self.result_tree.column("提取码", width=60, anchor="center")
        self.result_tree.column("状态", width=60, anchor="center")
        
        # 添加滚动条
        result_scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=self.result_tree.yview)
        self.result_tree.configure(yscrollcommand=result_scrollbar.set)
        
        # 布局
        self.result_tree.pack(side="left", fill="both", expand=True)
        result_scrollbar.pack(side="right", fill="y")
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_var = tk.StringVar()
        self.status_var.set("🟢 就绪")
        
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief="sunken", anchor="w")
        status_bar.pack(side="bottom", fill="x")
    
    def center_window(self):
        """窗口居中"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def show_login_dialog(self):
        """显示登录对话框"""
        print("显示登录对话框被调用")  # 调试信息
        self.logger.info("显示登录对话框")
        try:
            dialog = LoginDialog(self.root)
            if dialog.result:
                username, password = dialog.result
                self.login(username, password)
            else:
                self.logger.info("用户取消登录")
        except Exception as e:
            self.logger.error(f"显示登录对话框失败: {e}")
            messagebox.showerror("错误", f"显示登录对话框失败: {e}")
    
    def login(self, username, password):
        """登录"""
        try:
            self.logger.info(f"尝试登录用户: {username}")
            self.status_var.set("🔄 正在登录...")
            
            if self.quark_api.login(username, password):
                self.login_status_label.config(text=f"已登录: {username}", foreground="green")
                self.login_button.config(state="disabled")
                self.logout_button.config(state="normal")
                self.refresh_button.config(state="normal")
                self.status_var.set("✅ 登录成功")
                self.logger.info(f"用户 {username} 登录成功")
                self.refresh_file_list()
            else:
                self.logger.warning(f"用户 {username} 登录失败：用户名或密码错误")
                messagebox.showerror("登录失败", "用户名或密码错误")
                self.status_var.set("❌ 登录失败")
                
        except Exception as e:
            self.logger.error(f"登录时发生错误: {e}")
            messagebox.showerror("登录错误", f"登录时发生错误: {e}")
            self.status_var.set("❌ 登录错误")
    
    def logout(self):
        """登出"""
        self.quark_api.logout()
        self.login_status_label.config(text="未登录", foreground="red")
        self.login_button.config(state="normal")
        self.logout_button.config(state="disabled")
        self.refresh_button.config(state="disabled")
        self.select_all_button.config(state="disabled")
        self.deselect_all_button.config(state="disabled")
        self.batch_share_button.config(state="disabled")
        
        # 清空文件列表
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)
        
        self.status_var.set("🚪 已登出")
    
    def refresh_file_list(self):
        """刷新文件列表"""
        try:
            self.status_var.set("🔄 正在获取文件列表...")
            
            # 清空现有列表
            for item in self.file_tree.get_children():
                self.file_tree.delete(item)
            self.selected_files.clear()
            
            # 获取文件列表
            files = self.quark_api.get_files()
            
            # 添加到树形控件
            for file_obj in files:
                size_str = self._format_file_size(file_obj.size) if file_obj.file_type == "file" else "-"
                type_str = "📁 文件夹" if file_obj.file_type == "folder" else "📄 文件"
                
                self.file_tree.insert("", "end", values=("☐", file_obj.name, size_str, type_str), 
                                    tags=(file_obj.file_id,))
            
            # 启用操作按钮
            self.select_all_button.config(state="normal")
            self.deselect_all_button.config(state="normal")
            self.batch_share_button.config(state="normal")
            
            self.status_var.set(f"✅ 已加载 {len(files)} 个文件")
            
        except Exception as e:
            messagebox.showerror("错误", f"获取文件列表失败: {e}")
            self.status_var.set("❌ 获取文件列表失败")
    
    def _format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
    
    def toggle_file_selection(self, event=None):
        """切换文件选择状态"""
        try:
            selection = self.file_tree.selection()
            if not selection:
                return
            
            item = selection[0]
            file_id = self.file_tree.item(item, "tags")[0]
            current_values = list(self.file_tree.item(item, "values"))
            
            # 切换选择状态
            if current_values[0] == "☐":
                current_values[0] = "☑"
                self.selected_files.add(file_id)
            else:
                current_values[0] = "☐"
                self.selected_files.discard(file_id)
            
            self.file_tree.item(item, values=current_values)
            self.logger.debug(f"切换文件选择状态: {current_values[1]}")
            
        except Exception as e:
            self.logger.error(f"切换文件选择状态失败: {e}")
            messagebox.showerror("错误", f"操作失败: {e}")
    
    def select_all_files(self):
        """全选文件"""
        try:
            for item in self.file_tree.get_children():
                file_id = self.file_tree.item(item, "tags")[0]
                current_values = list(self.file_tree.item(item, "values"))
                current_values[0] = "☑"
                self.file_tree.item(item, values=current_values)
                self.selected_files.add(file_id)
            
            self.status_var.set("✅ 已全选所有文件")
            
        except Exception as e:
            self.logger.error(f"全选文件失败: {e}")
            messagebox.showerror("错误", f"全选失败: {e}")
    
    def deselect_all_files(self):
        """取消全选文件"""
        try:
            for item in self.file_tree.get_children():
                current_values = list(self.file_tree.item(item, "values"))
                current_values[0] = "☐"
                self.file_tree.item(item, values=current_values)
            
            self.selected_files.clear()
            self.status_var.set("✅ 已取消选择所有文件")
            
        except Exception as e:
            self.logger.error(f"取消全选失败: {e}")
            messagebox.showerror("错误", f"取消全选失败: {e}")
    
    def batch_share(self):
        """批量分享"""
        if not self.selected_files:
            messagebox.showwarning("警告", "请先选择要分享的文件")
            return
        
        selected_list = list(self.selected_files)
        
        # 显示进度对话框并开始分享
        progress_dialog = ProgressDialog(self.root, "🚀 批量分享进度", len(selected_list))
        
        def share_worker():
            try:
                self.share_results = self.quark_api.batch_create_share_links(
                    selected_list, 
                    expire_days=7, 
                    need_password=True,
                    progress_callback=progress_dialog.update_progress
                )
                
                # 在主线程中更新UI
                self.root.after(0, self.update_result_display)
                self.root.after(0, progress_dialog.close)
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"批量分享失败: {e}"))
                self.root.after(0, progress_dialog.close)
        
        # 启动工作线程
        thread = threading.Thread(target=share_worker)
        thread.daemon = True
        thread.start()
        
        self.status_var.set(f"🚀 正在分享 {len(selected_list)} 个文件...")
    
    def update_result_display(self):
        """更新结果显示"""
        # 清空现有结果
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)
        
        # 添加新结果
        for result in self.share_results:
            short_link = result["share_link"][:30] + "..." if len(result["share_link"]) > 30 else result["share_link"]
            
            self.result_tree.insert("", "end", values=(
                result["file_name"],
                short_link,
                result["share_code"],
                result["status"]
            ))
        
        # 启用导出按钮
        if self.share_results:
            self.export_button.config(state="normal")
        
        # 更新状态
        success_count = sum(1 for r in self.share_results if r["status"] == "成功")
        total_count = len(self.share_results)
        self.status_var.set(f"✅ 分享完成: {success_count}/{total_count} 成功")
    
    def export_to_excel(self):
        """导出到Excel"""
        if not self.share_results:
            messagebox.showwarning("警告", "没有分享结果可导出")
            return
        
        # 选择保存路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"夸克网盘分享记录_{timestamp}.xlsx"
        
        file_path = filedialog.asksaveasfilename(
            title="保存Excel文件",
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")],
            initialvalue=default_filename
        )
        
        if file_path:
            try:
                self.status_var.set("📄 正在导出Excel...")
                output_path = self.excel_exporter.export_share_results(self.share_results, file_path)
                messagebox.showinfo("成功", f"Excel文件已导出到:\n{output_path}")
                self.status_var.set("✅ Excel导出成功")
            except Exception as e:
                messagebox.showerror("错误", f"导出Excel失败: {e}")
                self.status_var.set("❌ Excel导出失败")
