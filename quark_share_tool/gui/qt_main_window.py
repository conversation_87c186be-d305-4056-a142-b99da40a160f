#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt6版本的主窗口GUI
现代化、美观、功能完整的图形界面
"""

import sys
import threading
from datetime import datetime
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QGridLayout, QPushButton, QLabel, 
                            QTreeWidget, QTreeWidgetItem, QStatusBar, QSplitter,
                            QGroupBox, QLineEdit, QMessageBox, QFileDialog,
                            QProgressDialog, QDialog, QDialogButtonBox,
                            QFormLayout, QCheckBox, QFrame)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QObject, QThread
from PyQt6.QtGui import QIcon, QFont, QPalette, QColor

from core.quark_api import QuarkAPI
from utils.excel_exporter import ExcelExporter
from utils.logger import setup_logger

class LoginDialog(QDialog):
    """登录对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.result = None
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("夸克网盘登录")
        self.setFixedSize(350, 200)
        self.setModal(True)

        # 应用深色主题样式
        self.apply_dialog_styles()

        layout = QVBoxLayout()

        # 标题
        title = QLabel("夸克网盘登录")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setFont(QFont("", 14, QFont.Weight.Bold))
        layout.addWidget(title)

        # 表单
        form_layout = QFormLayout()

        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("请输入用户名")
        form_layout.addRow("用户名:", self.username_edit)

        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_edit.setPlaceholderText("请输入密码")
        form_layout.addRow("密码:", self.password_edit)

        layout.addLayout(form_layout)

        # 提示
        tip = QLabel("注意：这是演示版本，任意用户名密码都可以登录")
        tip.setObjectName("tipLabel")
        tip.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(tip)

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.button(QDialogButtonBox.StandardButton.Ok).setText("登录")
        button_box.button(QDialogButtonBox.StandardButton.Cancel).setText("取消")
        button_box.accepted.connect(self.accept_login)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        self.setLayout(layout)

        # 设置焦点
        self.username_edit.setFocus()

        # 回车键登录
        self.username_edit.returnPressed.connect(self.accept_login)
        self.password_edit.returnPressed.connect(self.accept_login)

    def apply_dialog_styles(self):
        """应用对话框样式"""
        # 检测系统主题
        app = QApplication.instance()
        palette = app.palette()
        is_dark_theme = palette.color(QPalette.ColorRole.Window).lightness() < 128

        if is_dark_theme:
            # 深色主题
            self.setStyleSheet("""
                QDialog {
                    background-color: #2b2b2b;
                    color: #ffffff;
                }
                QLabel {
                    color: #ffffff;
                }
                QLabel#tipLabel {
                    color: #888888;
                    font-size: 10px;
                }
                QLineEdit {
                    background-color: #404040;
                    border: 1px solid #555;
                    padding: 6px;
                    border-radius: 4px;
                    color: #ffffff;
                    font-size: 12px;
                }
                QLineEdit:focus {
                    border: 2px solid #2196F3;
                }
                QPushButton {
                    background-color: #404040;
                    border: 1px solid #555;
                    padding: 8px 16px;
                    border-radius: 4px;
                    color: #ffffff;
                    font-size: 12px;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #505050;
                }
                QPushButton:pressed {
                    background-color: #606060;
                }
                QPushButton:default {
                    background-color: #2196F3;
                }
                QPushButton:default:hover {
                    background-color: #1976D2;
                }
            """)
        else:
            # 浅色主题
            self.setStyleSheet("""
                QDialog {
                    background-color: #ffffff;
                    color: #000000;
                }
                QLabel {
                    color: #000000;
                }
                QLabel#tipLabel {
                    color: #666666;
                    font-size: 10px;
                }
                QLineEdit {
                    background-color: #ffffff;
                    border: 1px solid #ccc;
                    padding: 6px;
                    border-radius: 4px;
                    color: #000000;
                    font-size: 12px;
                }
                QLineEdit:focus {
                    border: 2px solid #2196F3;
                }
                QPushButton {
                    background-color: #f0f0f0;
                    border: 1px solid #ccc;
                    padding: 8px 16px;
                    border-radius: 4px;
                    color: #000000;
                    font-size: 12px;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                }
                QPushButton:pressed {
                    background-color: #d0d0d0;
                }
                QPushButton:default {
                    background-color: #2196F3;
                    color: #ffffff;
                }
                QPushButton:default:hover {
                    background-color: #1976D2;
                }
            """)
    
    def accept_login(self):
        """确认登录"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()
        
        if not username:
            QMessageBox.warning(self, "警告", "请输入用户名")
            self.username_edit.setFocus()
            return
            
        if not password:
            QMessageBox.warning(self, "警告", "请输入密码")
            self.password_edit.setFocus()
            return
        
        self.result = (username, password)
        self.accept()

class ShareWorker(QObject):
    """分享工作线程"""
    progress_updated = pyqtSignal(int, int, str)
    finished = pyqtSignal(list)
    error = pyqtSignal(str)
    
    def __init__(self, quark_api, file_ids):
        super().__init__()
        self.quark_api = quark_api
        self.file_ids = file_ids
    
    def run(self):
        """执行分享任务"""
        try:
            results = self.quark_api.batch_create_share_links(
                self.file_ids,
                expire_days=7,
                need_password=True,
                progress_callback=self.progress_updated.emit
            )
            self.finished.emit(results)
        except Exception as e:
            self.error.emit(str(e))

class QuarkShareMainWindow(QMainWindow):
    """PyQt6版本的主窗口"""
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger()
        self.quark_api = QuarkAPI()
        self.excel_exporter = ExcelExporter()
        self.share_results = []
        self.selected_files = set()
        self.is_dark_theme = False  # 默认浅色主题
        
        self.setup_ui()
        self.setup_timer()
        self.center_window()
        
        self.logger.info("PyQt6版夸克网盘批量分享工具启动")
    
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("🌟 夸克网盘批量分享工具 v3.0 (PyQt6版)")
        self.setMinimumSize(1200, 800)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建工具栏
        self.create_toolbar(main_layout)
        
        # 创建主要内容区域
        self.create_main_content(main_layout)
        
        # 创建状态栏
        self.create_status_bar()
        
        # 应用样式
        self.apply_styles()
    
    def create_toolbar(self, parent_layout):
        """创建工具栏"""
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.Shape.Box)
        toolbar_frame.setStyleSheet("QFrame { border: 1px solid #ccc; background-color: #f5f5f5; }")
        
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        # 登录区域
        login_group = QGroupBox("账户状态")
        login_layout = QHBoxLayout(login_group)
        
        self.login_status_label = QLabel("● 未登录")
        self.login_status_label.setStyleSheet("color: red; font-weight: bold;")
        login_layout.addWidget(self.login_status_label)
        
        self.login_button = QPushButton("🔑 登录")
        self.login_button.clicked.connect(self.show_login_dialog)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """)
        login_layout.addWidget(self.login_button)
        
        self.logout_button = QPushButton("🚪 登出")
        self.logout_button.clicked.connect(self.logout)
        self.logout_button.setEnabled(False)
        self.logout_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:disabled {
                background-color: #ccc;
                color: #666;
            }
        """)
        login_layout.addWidget(self.logout_button)
        
        toolbar_layout.addWidget(login_group)
        toolbar_layout.addStretch()
        
        # 主题切换按钮
        self.theme_button = QPushButton("🌙 深色主题")
        self.theme_button.clicked.connect(self.toggle_theme)
        self.theme_button.setStyleSheet("""
            QPushButton {
                background-color: #6A1B9A;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4A148C;
            }
        """)
        toolbar_layout.addWidget(self.theme_button)

        # 时间显示
        self.time_label = QLabel()
        self.time_label.setStyleSheet("color: #666; font-size: 12px;")
        toolbar_layout.addWidget(self.time_label)
        
        parent_layout.addWidget(toolbar_frame)
    
    def create_main_content(self, parent_layout):
        """创建主要内容区域"""
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：文件管理
        self.create_file_management_area(splitter)
        
        # 右侧：分享结果
        self.create_share_results_area(splitter)
        
        # 设置分割比例
        splitter.setSizes([600, 400])
        
        parent_layout.addWidget(splitter)
    
    def create_file_management_area(self, parent):
        """创建文件管理区域"""
        file_widget = QWidget()
        file_layout = QVBoxLayout(file_widget)
        
        # 标题
        title = QLabel("📁 文件管理")
        title.setFont(QFont("", 12, QFont.Weight.Bold))
        file_layout.addWidget(title)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.refresh_button = QPushButton("🔄 刷新列表")
        self.refresh_button.clicked.connect(self.refresh_file_list)
        self.refresh_button.setEnabled(False)
        button_layout.addWidget(self.refresh_button)
        
        self.select_all_button = QPushButton("☑️ 全选")
        self.select_all_button.clicked.connect(self.select_all_files)
        self.select_all_button.setEnabled(False)
        button_layout.addWidget(self.select_all_button)
        
        self.deselect_all_button = QPushButton("☐ 取消全选")
        self.deselect_all_button.clicked.connect(self.deselect_all_files)
        self.deselect_all_button.setEnabled(False)
        button_layout.addWidget(self.deselect_all_button)
        
        self.batch_share_button = QPushButton("🚀 批量分享")
        self.batch_share_button.clicked.connect(self.batch_share)
        self.batch_share_button.setEnabled(False)
        self.batch_share_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #388E3C;
            }
            QPushButton:disabled {
                background-color: #ccc;
                color: #666;
            }
        """)
        button_layout.addWidget(self.batch_share_button)
        
        button_layout.addStretch()
        
        # 文件统计
        self.file_stats_label = QLabel("文件: 0 | 已选: 0")
        self.file_stats_label.setStyleSheet("color: #666; font-size: 12px;")
        button_layout.addWidget(self.file_stats_label)
        
        file_layout.addLayout(button_layout)
        
        # 搜索框
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("🔍 搜索:"))
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入文件名搜索...")
        self.search_edit.textChanged.connect(self.filter_files)
        search_layout.addWidget(self.search_edit)
        
        clear_button = QPushButton("✖")
        clear_button.clicked.connect(lambda: self.search_edit.clear())
        clear_button.setFixedSize(30, 30)
        search_layout.addWidget(clear_button)
        
        file_layout.addLayout(search_layout)
        
        # 文件列表
        self.file_tree = QTreeWidget()
        self.file_tree.setHeaderLabels(["选择", "文件名", "大小", "类型", "修改时间"])
        self.file_tree.setColumnWidth(0, 50)
        self.file_tree.setColumnWidth(1, 250)
        self.file_tree.setColumnWidth(2, 80)
        self.file_tree.setColumnWidth(3, 80)
        self.file_tree.setColumnWidth(4, 120)
        
        # 双击切换选择
        self.file_tree.itemDoubleClicked.connect(self.toggle_file_selection)
        
        file_layout.addWidget(self.file_tree)
        
        parent.addWidget(file_widget)
    
    def create_share_results_area(self, parent):
        """创建分享结果区域"""
        result_widget = QWidget()
        result_layout = QVBoxLayout(result_widget)
        
        # 标题
        title = QLabel("📊 分享结果")
        title.setFont(QFont("", 12, QFont.Weight.Bold))
        result_layout.addWidget(title)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.export_button = QPushButton("📄 导出Excel")
        self.export_button.clicked.connect(self.export_to_excel)
        self.export_button.setEnabled(False)
        self.export_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #388E3C;
            }
            QPushButton:disabled {
                background-color: #ccc;
                color: #666;
            }
        """)
        button_layout.addWidget(self.export_button)
        
        self.clear_results_button = QPushButton("🗑️ 清空结果")
        self.clear_results_button.clicked.connect(self.clear_results)
        button_layout.addWidget(self.clear_results_button)
        
        button_layout.addStretch()
        
        # 结果统计
        self.result_stats_label = QLabel("结果: 0 | 成功: 0 | 失败: 0")
        self.result_stats_label.setStyleSheet("color: #666; font-size: 12px;")
        button_layout.addWidget(self.result_stats_label)
        
        result_layout.addLayout(button_layout)
        
        # 结果列表
        self.result_tree = QTreeWidget()
        self.result_tree.setHeaderLabels(["状态", "文件名", "分享链接", "提取码", "创建时间"])
        self.result_tree.setColumnWidth(0, 60)
        self.result_tree.setColumnWidth(1, 150)
        self.result_tree.setColumnWidth(2, 200)
        self.result_tree.setColumnWidth(3, 80)
        self.result_tree.setColumnWidth(4, 120)
        
        # 双击复制链接
        self.result_tree.itemDoubleClicked.connect(self.copy_share_link)
        
        result_layout.addWidget(self.result_tree)
        
        parent.addWidget(result_widget)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("🟢 就绪")

    def setup_timer(self):
        """设置定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)  # 每秒更新一次
        self.update_time()

    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("⏰ %Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)

    def apply_styles(self):
        """应用样式 - 深色主题适配"""
        # 使用手动设置的主题，如果没有设置则检测系统主题
        if not hasattr(self, 'is_dark_theme'):
            palette = self.palette()
            self.is_dark_theme = palette.color(QPalette.ColorRole.Window).lightness() < 128

        is_dark_theme = self.is_dark_theme

        if is_dark_theme:
            # 深色主题样式
            self.setStyleSheet("""
                QMainWindow {
                    background-color: #2b2b2b;
                    color: #ffffff;
                }
                QWidget {
                    background-color: #2b2b2b;
                    color: #ffffff;
                }
                QGroupBox {
                    font-weight: bold;
                    border: 2px solid #555;
                    border-radius: 5px;
                    margin-top: 10px;
                    padding-top: 10px;
                    background-color: #353535;
                    color: #ffffff;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                    color: #ffffff;
                }
                QPushButton {
                    background-color: #404040;
                    border: 1px solid #555;
                    padding: 6px 12px;
                    border-radius: 4px;
                    font-size: 12px;
                    color: #ffffff;
                }
                QPushButton:hover {
                    background-color: #505050;
                }
                QPushButton:pressed {
                    background-color: #606060;
                }
                QPushButton:disabled {
                    background-color: #2a2a2a;
                    color: #666;
                }
                QLineEdit {
                    border: 1px solid #555;
                    padding: 4px;
                    border-radius: 4px;
                    font-size: 12px;
                    background-color: #404040;
                    color: #ffffff;
                }
                QLineEdit:focus {
                    border: 2px solid #2196F3;
                }
                QTreeWidget {
                    border: 1px solid #555;
                    background-color: #353535;
                    alternate-background-color: #404040;
                    color: #ffffff;
                }
                QTreeWidget::item {
                    padding: 4px;
                    color: #ffffff;
                }
                QTreeWidget::item:selected {
                    background-color: #2196F3;
                    color: #ffffff;
                }
                QTreeWidget::item:hover {
                    background-color: #404040;
                }
                QHeaderView::section {
                    background-color: #404040;
                    color: #ffffff;
                    border: 1px solid #555;
                    padding: 4px;
                }
                QLabel {
                    color: #ffffff;
                }
                QStatusBar {
                    background-color: #353535;
                    color: #ffffff;
                    border-top: 1px solid #555;
                }
                QFrame {
                    background-color: #353535;
                    border: 1px solid #555;
                }
            """)
        else:
            # 浅色主题样式
            self.setStyleSheet("""
                QMainWindow {
                    background-color: #f5f5f5;
                    color: #000000;
                }
                QWidget {
                    background-color: #f5f5f5;
                    color: #000000;
                }
                QGroupBox {
                    font-weight: bold;
                    border: 2px solid #ccc;
                    border-radius: 5px;
                    margin-top: 10px;
                    padding-top: 10px;
                    background-color: #ffffff;
                    color: #000000;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                    color: #000000;
                }
                QPushButton {
                    background-color: #e0e0e0;
                    border: 1px solid #ccc;
                    padding: 6px 12px;
                    border-radius: 4px;
                    font-size: 12px;
                    color: #000000;
                }
                QPushButton:hover {
                    background-color: #d0d0d0;
                }
                QPushButton:pressed {
                    background-color: #c0c0c0;
                }
                QPushButton:disabled {
                    background-color: #f0f0f0;
                    color: #999;
                }
                QLineEdit {
                    border: 1px solid #ccc;
                    padding: 4px;
                    border-radius: 4px;
                    font-size: 12px;
                    background-color: #ffffff;
                    color: #000000;
                }
                QLineEdit:focus {
                    border: 2px solid #2196F3;
                }
                QTreeWidget {
                    border: 1px solid #ccc;
                    background-color: #ffffff;
                    alternate-background-color: #f9f9f9;
                    color: #000000;
                }
                QTreeWidget::item {
                    padding: 4px;
                    color: #000000;
                }
                QTreeWidget::item:selected {
                    background-color: #e3f2fd;
                    color: #000000;
                }
                QHeaderView::section {
                    background-color: #f0f0f0;
                    color: #000000;
                    border: 1px solid #ccc;
                    padding: 4px;
                }
                QLabel {
                    color: #000000;
                }
                QStatusBar {
                    background-color: #f0f0f0;
                    color: #000000;
                    border-top: 1px solid #ccc;
                }
                QFrame {
                    background-color: #ffffff;
                    border: 1px solid #ccc;
                }
            """)

    def center_window(self):
        """窗口居中"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def show_login_dialog(self):
        """显示登录对话框"""
        self.logger.info("显示登录对话框")
        dialog = LoginDialog(self)

        if dialog.exec() == QDialog.DialogCode.Accepted and dialog.result:
            username, password = dialog.result
            self.login(username, password)

    def login(self, username, password):
        """登录"""
        try:
            self.logger.info(f"尝试登录用户: {username}")
            self.status_bar.showMessage("🔄 正在登录...")

            if self.quark_api.login(username, password):
                # 更新UI状态
                self.login_status_label.setText(f"● 已登录: {username}")
                self.login_status_label.setStyleSheet("color: green; font-weight: bold;")
                self.login_button.setEnabled(False)
                self.logout_button.setEnabled(True)
                self.refresh_button.setEnabled(True)

                self.status_bar.showMessage("✅ 登录成功")
                self.logger.info(f"用户 {username} 登录成功")

                # 自动刷新文件列表
                self.refresh_file_list()
            else:
                self.logger.warning(f"用户 {username} 登录失败：用户名或密码错误")
                QMessageBox.critical(self, "登录失败", "用户名或密码错误")
                self.status_bar.showMessage("❌ 登录失败")

        except Exception as e:
            self.logger.error(f"登录时发生错误: {e}")
            QMessageBox.critical(self, "登录错误", f"登录时发生错误: {e}")
            self.status_bar.showMessage("❌ 登录错误")

    def logout(self):
        """登出"""
        self.quark_api.logout()

        # 更新UI状态
        self.login_status_label.setText("● 未登录")
        self.login_status_label.setStyleSheet("color: red; font-weight: bold;")
        self.login_button.setEnabled(True)
        self.logout_button.setEnabled(False)
        self.refresh_button.setEnabled(False)
        self.select_all_button.setEnabled(False)
        self.deselect_all_button.setEnabled(False)
        self.batch_share_button.setEnabled(False)

        # 清空文件列表
        self.file_tree.clear()
        self.selected_files.clear()
        self.update_file_stats()

        self.status_bar.showMessage("🚪 已登出")

    def refresh_file_list(self):
        """刷新文件列表"""
        try:
            self.status_bar.showMessage("🔄 正在获取文件列表...")

            # 清空现有列表
            self.file_tree.clear()
            self.selected_files.clear()

            # 获取文件列表
            files = self.quark_api.get_files()

            # 添加到树形控件
            for file_obj in files:
                item = QTreeWidgetItem()
                item.setText(0, "☐")  # 选择状态
                item.setText(1, file_obj.name)  # 文件名
                item.setText(2, self._format_file_size(file_obj.size) if file_obj.file_type == "file" else "-")  # 大小
                item.setText(3, "📁 文件夹" if file_obj.file_type == "folder" else "📄 文件")  # 类型
                item.setText(4, file_obj.created_time.strftime("%m-%d %H:%M"))  # 修改时间

                # 存储文件ID
                item.setData(0, Qt.ItemDataRole.UserRole, file_obj.file_id)

                self.file_tree.addTopLevelItem(item)

            # 启用操作按钮
            self.select_all_button.setEnabled(True)
            self.deselect_all_button.setEnabled(True)
            self.batch_share_button.setEnabled(True)

            self.update_file_stats()
            self.status_bar.showMessage(f"✅ 已加载 {len(files)} 个文件")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"获取文件列表失败: {e}")
            self.status_bar.showMessage("❌ 获取文件列表失败")

    def _format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)

        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1

        return f"{size:.1f} {size_names[i]}"

    def update_file_stats(self):
        """更新文件统计信息"""
        total_files = self.file_tree.topLevelItemCount()
        selected_count = len(self.selected_files)
        self.file_stats_label.setText(f"文件: {total_files} | 已选: {selected_count}")

    def toggle_file_selection(self, item, column):
        """切换文件选择状态"""
        try:
            file_id = item.data(0, Qt.ItemDataRole.UserRole)
            current_status = item.text(0)

            if current_status == "☐":
                item.setText(0, "☑")
                self.selected_files.add(file_id)
            else:
                item.setText(0, "☐")
                self.selected_files.discard(file_id)

            self.update_file_stats()
            self.logger.debug(f"切换文件选择状态: {item.text(1)}")

        except Exception as e:
            self.logger.error(f"切换文件选择状态失败: {e}")
            QMessageBox.critical(self, "错误", f"操作失败: {e}")

    def select_all_files(self):
        """全选文件"""
        try:
            for i in range(self.file_tree.topLevelItemCount()):
                item = self.file_tree.topLevelItem(i)
                file_id = item.data(0, Qt.ItemDataRole.UserRole)
                item.setText(0, "☑")
                self.selected_files.add(file_id)

            self.update_file_stats()
            self.status_bar.showMessage("✅ 已全选所有文件")

        except Exception as e:
            self.logger.error(f"全选文件失败: {e}")
            QMessageBox.critical(self, "错误", f"全选失败: {e}")

    def deselect_all_files(self):
        """取消全选文件"""
        try:
            for i in range(self.file_tree.topLevelItemCount()):
                item = self.file_tree.topLevelItem(i)
                item.setText(0, "☐")

            self.selected_files.clear()
            self.update_file_stats()
            self.status_bar.showMessage("✅ 已取消选择所有文件")

        except Exception as e:
            self.logger.error(f"取消全选失败: {e}")
            QMessageBox.critical(self, "错误", f"取消全选失败: {e}")

    def filter_files(self, text):
        """过滤文件"""
        for i in range(self.file_tree.topLevelItemCount()):
            item = self.file_tree.topLevelItem(i)
            file_name = item.text(1).lower()
            item.setHidden(text.lower() not in file_name)

    def batch_share(self):
        """批量分享"""
        if not self.selected_files:
            QMessageBox.warning(self, "警告", "请先选择要分享的文件")
            return

        selected_list = list(self.selected_files)

        # 创建进度对话框
        progress = QProgressDialog("正在批量分享文件...", "取消", 0, len(selected_list), self)
        progress.setWindowTitle("批量分享进度")
        progress.setModal(True)
        progress.show()

        # 创建工作线程
        self.share_thread = QThread()
        self.share_worker = ShareWorker(self.quark_api, selected_list)
        self.share_worker.moveToThread(self.share_thread)

        # 连接信号
        self.share_thread.started.connect(self.share_worker.run)
        self.share_worker.progress_updated.connect(lambda current, total, filename: (
            progress.setValue(current),
            progress.setLabelText(f"正在处理: {filename}")
        ))
        self.share_worker.finished.connect(self.on_share_finished)
        self.share_worker.error.connect(self.on_share_error)
        self.share_worker.finished.connect(self.share_thread.quit)
        self.share_worker.error.connect(self.share_thread.quit)
        self.share_thread.finished.connect(lambda: progress.close())

        # 启动线程
        self.share_thread.start()

        self.status_bar.showMessage(f"🚀 正在分享 {len(selected_list)} 个文件...")

    def on_share_finished(self, results):
        """分享完成"""
        self.share_results = results
        self.update_result_display()

    def on_share_error(self, error_msg):
        """分享出错"""
        QMessageBox.critical(self, "错误", f"批量分享失败: {error_msg}")
        self.status_bar.showMessage("❌ 批量分享失败")

    def update_result_display(self):
        """更新结果显示"""
        # 清空现有结果
        self.result_tree.clear()

        # 添加新结果
        for result in self.share_results:
            item = QTreeWidgetItem()
            status_icon = "✅" if result["status"] == "成功" else "❌"
            short_link = result["share_link"][:30] + "..." if len(result["share_link"]) > 30 else result["share_link"]

            item.setText(0, status_icon)
            item.setText(1, result["file_name"])
            item.setText(2, short_link)
            item.setText(3, result["share_code"])
            item.setText(4, result["created_time"])

            # 存储完整链接
            item.setData(2, Qt.ItemDataRole.UserRole, result["share_link"])

            self.result_tree.addTopLevelItem(item)

        # 启用导出按钮
        if self.share_results:
            self.export_button.setEnabled(True)

        # 更新统计信息
        self.update_result_stats()

        # 更新状态
        success_count = sum(1 for r in self.share_results if r["status"] == "成功")
        total_count = len(self.share_results)
        self.status_bar.showMessage(f"✅ 分享完成: {success_count}/{total_count} 成功")

    def update_result_stats(self):
        """更新结果统计信息"""
        total = len(self.share_results)
        success = sum(1 for r in self.share_results if r["status"] == "成功")
        failed = total - success
        self.result_stats_label.setText(f"结果: {total} | 成功: {success} | 失败: {failed}")

    def copy_share_link(self, item, column):
        """复制分享链接"""
        try:
            full_link = item.data(2, Qt.ItemDataRole.UserRole)
            if full_link:
                clipboard = QApplication.clipboard()
                clipboard.setText(full_link)
                self.status_bar.showMessage(f"📋 已复制分享链接: {item.text(1)}")

        except Exception as e:
            self.logger.error(f"复制分享链接失败: {e}")
            QMessageBox.critical(self, "错误", f"复制失败: {e}")

    def clear_results(self):
        """清空结果"""
        reply = QMessageBox.question(self, "确认", "确定要清空所有分享结果吗？",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            self.result_tree.clear()
            self.share_results.clear()
            self.export_button.setEnabled(False)
            self.update_result_stats()
            self.status_bar.showMessage("🗑️ 已清空分享结果")

    def export_to_excel(self):
        """导出到Excel"""
        if not self.share_results:
            QMessageBox.warning(self, "警告", "没有分享结果可导出")
            return

        # 选择保存路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"夸克网盘分享记录_{timestamp}.xlsx"

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存Excel文件", default_filename,
            "Excel文件 (*.xlsx);;所有文件 (*.*)"
        )

        if file_path:
            try:
                self.status_bar.showMessage("📄 正在导出Excel...")

                # 创建进度对话框
                progress = QProgressDialog("正在导出Excel文件...", None, 0, 100, self)
                progress.setWindowTitle("导出进度")
                progress.setModal(True)
                progress.setValue(50)
                progress.show()

                output_path = self.excel_exporter.export_share_results(self.share_results, file_path)

                progress.setValue(100)
                progress.close()

                QMessageBox.information(self, "成功", f"Excel文件已导出到:\n{output_path}")
                self.status_bar.showMessage("✅ Excel导出成功")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出Excel失败: {e}")
                self.status_bar.showMessage("❌ Excel导出失败")

    def toggle_theme(self):
        """切换主题"""
        self.is_dark_theme = not self.is_dark_theme

        # 更新主题按钮文本
        if self.is_dark_theme:
            self.theme_button.setText("☀️ 浅色主题")
        else:
            self.theme_button.setText("🌙 深色主题")

        # 重新应用样式
        self.apply_styles()

        # 更新状态栏
        theme_name = "深色" if self.is_dark_theme else "浅色"
        self.status_bar.showMessage(f"🎨 已切换到{theme_name}主题")

        self.logger.info(f"切换到{theme_name}主题")

    def closeEvent(self, event):
        """关闭事件"""
        self.logger.info("PyQt6版夸克网盘批量分享工具退出")
        event.accept()
