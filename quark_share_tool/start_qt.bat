@echo off
echo 🌟 夸克网盘批量分享工具 PyQt6版
echo ==================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

echo ✅ 使用Python: 
python --version

REM 检查虚拟环境是否存在
if not exist "venv_qt" (
    echo 📦 创建虚拟环境...
    python -m venv venv_qt
    if errorlevel 1 (
        echo ❌ 错误：创建虚拟环境失败
        pause
        exit /b 1
    )
)

REM 激活虚拟环境
echo 🔧 激活虚拟环境...
call venv_qt\Scripts\activate.bat

REM 检查依赖是否安装
echo 📋 检查依赖包...
python -c "import PyQt6" >nul 2>&1
if errorlevel 1 (
    echo 📦 正在安装依赖包...
    pip install PyQt6 openpyxl requests
    if errorlevel 1 (
        echo ❌ 错误：依赖包安装失败
        pause
        exit /b 1
    )
)

echo ✅ 依赖检查完成

REM 启动程序
echo 🚀 启动程序...
python main_qt.py

echo 👋 程序已退出
pause
