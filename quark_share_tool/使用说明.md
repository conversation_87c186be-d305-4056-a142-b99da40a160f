# 夸克网盘批量分享工具 - 使用说明

## 快速开始

### 1. 启动程序

**方法一：使用启动脚本**
- Windows: 双击 `start.bat`
- macOS/Linux: 在终端中运行 `./start.sh`

**方法二：直接运行**
```bash
python3 main.py
```

### 2. 登录账户

1. 点击界面左上角的"登录"按钮
2. 在弹出的登录对话框中输入：
   - 用户名：任意用户名（演示版本）
   - 密码：任意密码（演示版本）
3. 点击"登录"按钮

> 注意：这是演示版本，任意用户名和密码都可以成功登录

### 3. 选择要分享的文件

登录成功后，程序会自动加载演示文件列表，包含以下类型的文件：
- 文档文件（PDF、DOCX等）
- 压缩文件（ZIP、RAR等）
- 视频文件（MP4等）
- 文件夹

**选择文件的方法：**
- **双击文件行**：切换单个文件的选择状态
- **全选按钮**：选择所有文件
- **取消全选按钮**：取消选择所有文件

选中的文件会显示 ☑ 标记，未选中的显示 ☐ 标记。

### 4. 批量创建分享链接

1. 选择好要分享的文件后，点击"批量分享"按钮
2. 程序会显示进度对话框，实时显示：
   - 当前处理的文件名
   - 处理进度百分比
   - 已完成/总数量
3. 等待所有文件处理完成

### 5. 查看分享结果

批量分享完成后，右侧的"分享结果"区域会显示：
- **文件名**：被分享的文件名称
- **分享链接**：生成的分享链接（演示数据）
- **提取码**：4位随机提取码
- **状态**：分享是否成功

### 6. 导出Excel表格

1. 分享完成后，"导出Excel"按钮会变为可用状态
2. 点击"导出Excel"按钮
3. 在文件保存对话框中选择保存位置和文件名
4. 点击"保存"

**Excel表格包含以下信息：**
- 序号
- 文件名
- 文件大小（自动格式化为KB/MB/GB）
- 分享链接
- 提取码
- 过期时间
- 创建时间
- 状态（成功/失败）

**Excel表格还包含统计信息：**
- 总文件数
- 成功数
- 失败数
- 成功率
- 导出时间

## 界面说明

### 主界面布局

```
┌─────────────────────────────────────────────────────────────┐
│ 账户登录: [未登录] [登录] [登出]                              │
├─────────────────────────────────────────────────────────────┤
│ 批量操作: [刷新文件列表] [全选] [取消全选] [批量分享] [导出Excel] │
├─────────────────────────────────────────────────────────────┤
│ 文件列表                    │ 分享结果                        │
│ ☐ 重要文档.pdf             │ 重要文档.pdf                    │
│ ☑ 项目资料.zip             │ https://pan.quark.cn/s/...      │
│ ☐ 照片集合                 │ abc4                            │
│ ...                        │ 成功                            │
└─────────────────────────────────────────────────────────────┘
│ 状态栏: 就绪                                                │
└─────────────────────────────────────────────────────────────┘
```

### 按钮功能说明

- **登录/登出**：管理账户登录状态
- **刷新文件列表**：重新获取网盘文件列表
- **全选/取消全选**：快速选择或取消选择所有文件
- **批量分享**：对选中的文件创建分享链接
- **导出Excel**：将分享结果导出为Excel文件

## 注意事项

### 演示版本说明
- 这是一个功能演示版本，使用模拟的夸克网盘API
- 生成的分享链接为演示数据，不是真实的分享链接
- 文件列表为预设的演示数据

### 使用建议
- 建议单次分享的文件数量不超过100个
- 大量文件分享时请耐心等待进度完成
- 定期导出分享记录以备份重要信息

### 数据安全
- 程序不会保存用户密码
- 所有分享记录仅在本地存储
- 日志文件不包含敏感信息

## 常见问题

### Q: 程序无法启动怎么办？
A: 请检查：
1. Python版本是否为3.7+
2. 是否已安装所需依赖包
3. 运行 `python3 -m pip install -r requirements.txt` 安装依赖

### Q: 登录失败怎么办？
A: 演示版本任意用户名密码都可以登录，如果仍然失败请检查网络连接。

### Q: 分享进度卡住怎么办？
A: 请等待程序完成处理，如果长时间无响应可以重启程序。

### Q: Excel导出失败怎么办？
A: 请检查：
1. 是否有写入权限
2. 目标文件是否被其他程序占用
3. 磁盘空间是否充足

## 技术支持

如遇到其他问题，请查看：
1. 程序运行日志（logs目录下）
2. 控制台错误信息
3. README.md文档

---

**版本**: v1.0.0  
**更新日期**: 2024-06-24
