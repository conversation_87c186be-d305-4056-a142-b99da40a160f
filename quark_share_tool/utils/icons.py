#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图标和图形资源模块
使用Unicode字符和简单图形创建图标
"""

import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageDraw, ImageTk
import io
import base64

class IconManager:
    """图标管理器"""
    
    def __init__(self):
        self.icons = {}
        self._icons_created = False
    
    def _create_icons(self):
        """创建图标"""
        # 创建各种图标
        self.icons['login'] = self._create_login_icon()
        self.icons['logout'] = self._create_logout_icon()
        self.icons['refresh'] = self._create_refresh_icon()
        self.icons['folder'] = self._create_folder_icon()
        self.icons['file'] = self._create_file_icon()
        self.icons['share'] = self._create_share_icon()
        self.icons['excel'] = self._create_excel_icon()
        self.icons['select_all'] = self._create_select_all_icon()
        self.icons['deselect_all'] = self._create_deselect_all_icon()
        self.icons['success'] = self._create_success_icon()
        self.icons['error'] = self._create_error_icon()
        self.icons['warning'] = self._create_warning_icon()
        self.icons['info'] = self._create_info_icon()
    
    def _create_icon_base(self, size=16, bg_color='white'):
        """创建图标基础"""
        img = Image.new('RGBA', (size, size), (255, 255, 255, 0))
        draw = ImageDraw.Draw(img)
        return img, draw
    
    def _create_login_icon(self):
        """创建登录图标"""
        img, draw = self._create_icon_base(16)
        # 绘制用户图标
        draw.ellipse([3, 2, 13, 8], fill='#4CAF50', outline='#2E7D32')
        draw.rectangle([2, 9, 14, 15], fill='#4CAF50', outline='#2E7D32')
        return ImageTk.PhotoImage(img)
    
    def _create_logout_icon(self):
        """创建登出图标"""
        img, draw = self._create_icon_base(16)
        # 绘制登出图标
        draw.rectangle([2, 4, 10, 12], fill='#F44336', outline='#C62828')
        draw.polygon([(10, 6), (14, 8), (10, 10)], fill='#F44336', outline='#C62828')
        return ImageTk.PhotoImage(img)
    
    def _create_refresh_icon(self):
        """创建刷新图标"""
        img, draw = self._create_icon_base(16)
        # 绘制刷新图标（圆形箭头）
        draw.arc([2, 2, 14, 14], 0, 270, fill='#2196F3', width=2)
        draw.polygon([(12, 3), (14, 1), (14, 5)], fill='#2196F3')
        return ImageTk.PhotoImage(img)
    
    def _create_folder_icon(self):
        """创建文件夹图标"""
        img, draw = self._create_icon_base(16)
        # 绘制文件夹
        draw.rectangle([1, 6, 15, 14], fill='#FFC107', outline='#F57C00')
        draw.rectangle([1, 4, 8, 6], fill='#FFC107', outline='#F57C00')
        return ImageTk.PhotoImage(img)
    
    def _create_file_icon(self):
        """创建文件图标"""
        img, draw = self._create_icon_base(16)
        # 绘制文件
        draw.rectangle([3, 1, 11, 15], fill='#E3F2FD', outline='#1976D2')
        draw.polygon([(11, 1), (13, 3), (11, 3)], fill='#1976D2')
        draw.line([(5, 5), (9, 5)], fill='#1976D2', width=1)
        draw.line([(5, 7), (11, 7)], fill='#1976D2', width=1)
        draw.line([(5, 9), (11, 9)], fill='#1976D2', width=1)
        return ImageTk.PhotoImage(img)
    
    def _create_share_icon(self):
        """创建分享图标"""
        img, draw = self._create_icon_base(16)
        # 绘制分享图标
        draw.ellipse([1, 1, 5, 5], fill='#9C27B0', outline='#6A1B9A')
        draw.ellipse([11, 1, 15, 5], fill='#9C27B0', outline='#6A1B9A')
        draw.ellipse([6, 11, 10, 15], fill='#9C27B0', outline='#6A1B9A')
        draw.line([(4, 4), (7, 11)], fill='#9C27B0', width=2)
        draw.line([(12, 4), (9, 11)], fill='#9C27B0', width=2)
        return ImageTk.PhotoImage(img)
    
    def _create_excel_icon(self):
        """创建Excel图标"""
        img, draw = self._create_icon_base(16)
        # 绘制Excel图标
        draw.rectangle([2, 2, 14, 14], fill='#4CAF50', outline='#2E7D32')
        draw.text((4, 4), 'X', fill='white')
        return ImageTk.PhotoImage(img)
    
    def _create_select_all_icon(self):
        """创建全选图标"""
        img, draw = self._create_icon_base(16)
        # 绘制全选图标
        draw.rectangle([2, 2, 14, 14], fill='#2196F3', outline='#1565C0')
        draw.polygon([(4, 8), (7, 11), (12, 5)], fill='white')
        return ImageTk.PhotoImage(img)
    
    def _create_deselect_all_icon(self):
        """创建取消全选图标"""
        img, draw = self._create_icon_base(16)
        # 绘制取消全选图标
        draw.rectangle([2, 2, 14, 14], fill='#FF9800', outline='#E65100')
        draw.line([(5, 5), (11, 11)], fill='white', width=2)
        draw.line([(11, 5), (5, 11)], fill='white', width=2)
        return ImageTk.PhotoImage(img)
    
    def _create_success_icon(self):
        """创建成功图标"""
        img, draw = self._create_icon_base(16)
        draw.ellipse([1, 1, 15, 15], fill='#4CAF50', outline='#2E7D32')
        draw.polygon([(4, 8), (7, 11), (12, 5)], fill='white')
        return ImageTk.PhotoImage(img)
    
    def _create_error_icon(self):
        """创建错误图标"""
        img, draw = self._create_icon_base(16)
        draw.ellipse([1, 1, 15, 15], fill='#F44336', outline='#C62828')
        draw.line([(5, 5), (11, 11)], fill='white', width=2)
        draw.line([(11, 5), (5, 11)], fill='white', width=2)
        return ImageTk.PhotoImage(img)
    
    def _create_warning_icon(self):
        """创建警告图标"""
        img, draw = self._create_icon_base(16)
        draw.polygon([(8, 1), (15, 14), (1, 14)], fill='#FF9800', outline='#E65100')
        draw.text((7, 5), '!', fill='white')
        return ImageTk.PhotoImage(img)
    
    def _create_info_icon(self):
        """创建信息图标"""
        img, draw = self._create_icon_base(16)
        draw.ellipse([1, 1, 15, 15], fill='#2196F3', outline='#1565C0')
        draw.text((7, 4), 'i', fill='white')
        return ImageTk.PhotoImage(img)
    
    def get_icon(self, name):
        """获取图标"""
        if not self._icons_created:
            self._create_icons()
            self._icons_created = True
        return self.icons.get(name, None)
    
    def get_file_type_icon(self, file_type, file_name=""):
        """根据文件类型获取图标"""
        if not self._icons_created:
            self._create_icons()
            self._icons_created = True

        if file_type == "folder":
            return self.get_icon('folder')
        else:
            # 根据文件扩展名返回不同图标
            ext = file_name.lower().split('.')[-1] if '.' in file_name else ''
            if ext in ['xlsx', 'xls', 'csv']:
                return self.get_icon('excel')
            else:
                return self.get_icon('file')

# 全局图标管理器实例
icon_manager = IconManager()
