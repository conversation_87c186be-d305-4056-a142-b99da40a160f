#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题和样式管理模块
"""

import tkinter as tk
from tkinter import ttk

class ThemeManager:
    """主题管理器"""
    
    def __init__(self):
        self.current_theme = "light"
        self.themes = {
            "light": {
                "bg": "#FFFFFF",
                "fg": "#212121",
                "select_bg": "#E3F2FD",
                "select_fg": "#1976D2",
                "button_bg": "#2196F3",
                "button_fg": "#FFFFFF",
                "button_hover": "#1976D2",
                "entry_bg": "#FFFFFF",
                "entry_fg": "#212121",
                "frame_bg": "#F5F5F5",
                "header_bg": "#E0E0E0",
                "success": "#4CAF50",
                "error": "#F44336",
                "warning": "#FF9800",
                "info": "#2196F3"
            },
            "dark": {
                "bg": "#2B2B2B",
                "fg": "#FFFFFF",
                "select_bg": "#404040",
                "select_fg": "#64B5F6",
                "button_bg": "#1976D2",
                "button_fg": "#FFFFFF",
                "button_hover": "#1565C0",
                "entry_bg": "#404040",
                "entry_fg": "#FFFFFF",
                "frame_bg": "#353535",
                "header_bg": "#404040",
                "success": "#66BB6A",
                "error": "#EF5350",
                "warning": "#FFA726",
                "info": "#42A5F5"
            }
        }
    
    def get_color(self, color_name):
        """获取当前主题的颜色"""
        return self.themes[self.current_theme].get(color_name, "#000000")
    
    def set_theme(self, theme_name):
        """设置主题"""
        if theme_name in self.themes:
            self.current_theme = theme_name
    
    def apply_theme_to_widget(self, widget, widget_type="default"):
        """为控件应用主题"""
        theme = self.themes[self.current_theme]
        
        try:
            if widget_type == "button":
                widget.configure(
                    bg=theme["button_bg"],
                    fg=theme["button_fg"],
                    activebackground=theme["button_hover"],
                    activeforeground=theme["button_fg"],
                    relief="flat",
                    borderwidth=0,
                    padx=10,
                    pady=5
                )
            elif widget_type == "entry":
                widget.configure(
                    bg=theme["entry_bg"],
                    fg=theme["entry_fg"],
                    insertbackground=theme["fg"],
                    relief="solid",
                    borderwidth=1
                )
            elif widget_type == "frame":
                widget.configure(bg=theme["frame_bg"])
            elif widget_type == "label":
                widget.configure(
                    bg=theme["bg"],
                    fg=theme["fg"]
                )
            elif widget_type == "text":
                widget.configure(
                    bg=theme["bg"],
                    fg=theme["fg"],
                    insertbackground=theme["fg"],
                    selectbackground=theme["select_bg"],
                    selectforeground=theme["select_fg"]
                )
        except tk.TclError:
            # 某些控件可能不支持某些属性
            pass
    
    def configure_ttk_style(self, root):
        """配置ttk样式"""
        style = ttk.Style(root)
        theme = self.themes[self.current_theme]
        
        # 配置按钮样式
        style.configure(
            "Modern.TButton",
            background=theme["button_bg"],
            foreground=theme["button_fg"],
            borderwidth=0,
            focuscolor="none",
            padding=(10, 5)
        )
        
        style.map(
            "Modern.TButton",
            background=[("active", theme["button_hover"]),
                       ("pressed", theme["button_hover"])]
        )
        
        # 配置成功按钮样式
        style.configure(
            "Success.TButton",
            background=theme["success"],
            foreground="#FFFFFF",
            borderwidth=0,
            focuscolor="none",
            padding=(10, 5)
        )
        
        # 配置警告按钮样式
        style.configure(
            "Warning.TButton",
            background=theme["warning"],
            foreground="#FFFFFF",
            borderwidth=0,
            focuscolor="none",
            padding=(10, 5)
        )
        
        # 配置错误按钮样式
        style.configure(
            "Error.TButton",
            background=theme["error"],
            foreground="#FFFFFF",
            borderwidth=0,
            focuscolor="none",
            padding=(10, 5)
        )
        
        # 配置LabelFrame样式
        style.configure(
            "Modern.TLabelframe",
            background=theme["frame_bg"],
            borderwidth=1,
            relief="solid"
        )
        
        style.configure(
            "Modern.TLabelframe.Label",
            background=theme["frame_bg"],
            foreground=theme["fg"],
            font=("", 9, "bold")
        )
        
        # 配置Treeview样式
        style.configure(
            "Modern.Treeview",
            background=theme["bg"],
            foreground=theme["fg"],
            fieldbackground=theme["bg"],
            borderwidth=1,
            relief="solid"
        )
        
        style.configure(
            "Modern.Treeview.Heading",
            background=theme["header_bg"],
            foreground=theme["fg"],
            borderwidth=1,
            relief="solid",
            font=("", 9, "bold")
        )
        
        style.map(
            "Modern.Treeview",
            background=[("selected", theme["select_bg"])],
            foreground=[("selected", theme["select_fg"])]
        )
        
        # 配置进度条样式
        style.configure(
            "Modern.TProgressbar",
            background=theme["button_bg"],
            borderwidth=0,
            lightcolor=theme["button_bg"],
            darkcolor=theme["button_bg"]
        )
        
        # 配置Entry样式
        style.configure(
            "Modern.TEntry",
            fieldbackground=theme["entry_bg"],
            foreground=theme["entry_fg"],
            borderwidth=1,
            relief="solid"
        )

class ModernButton(tk.Button):
    """现代化按钮类"""
    
    def __init__(self, parent, text="", command=None, style="primary", icon=None, **kwargs):
        self.theme_manager = ThemeManager()
        
        # 设置默认样式
        default_config = {
            "text": text,
            "command": command,
            "relief": "flat",
            "borderwidth": 0,
            "padx": 15,
            "pady": 8,
            "font": ("", 9),
            "cursor": "hand2"
        }
        
        # 应用样式
        if style == "primary":
            default_config.update({
                "bg": self.theme_manager.get_color("button_bg"),
                "fg": self.theme_manager.get_color("button_fg"),
                "activebackground": self.theme_manager.get_color("button_hover"),
                "activeforeground": self.theme_manager.get_color("button_fg")
            })
        elif style == "success":
            default_config.update({
                "bg": self.theme_manager.get_color("success"),
                "fg": "#FFFFFF",
                "activebackground": "#388E3C",
                "activeforeground": "#FFFFFF"
            })
        elif style == "warning":
            default_config.update({
                "bg": self.theme_manager.get_color("warning"),
                "fg": "#FFFFFF",
                "activebackground": "#F57C00",
                "activeforeground": "#FFFFFF"
            })
        elif style == "error":
            default_config.update({
                "bg": self.theme_manager.get_color("error"),
                "fg": "#FFFFFF",
                "activebackground": "#D32F2F",
                "activeforeground": "#FFFFFF"
            })
        
        # 合并用户配置
        default_config.update(kwargs)
        
        super().__init__(parent, **default_config)
        
        # 添加图标
        if icon:
            self.configure(image=icon, compound="left")
        
        # 绑定悬停效果
        self.bind("<Enter>", self._on_enter)
        self.bind("<Leave>", self._on_leave)
        
        self.original_bg = self.cget("bg")
        self.hover_bg = self.cget("activebackground")
    
    def _on_enter(self, event):
        """鼠标悬停效果"""
        self.configure(bg=self.hover_bg)
    
    def _on_leave(self, event):
        """鼠标离开效果"""
        self.configure(bg=self.original_bg)

# 全局主题管理器实例
theme_manager = ThemeManager()
