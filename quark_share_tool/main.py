#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
夸克网盘批量分享工具
主程序入口
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui.simple_main_window import SimpleMainWindow
from core.quark_api import QuarkAPI
from utils.logger import setup_logger

def main():
    """主函数"""
    # 设置日志
    logger = setup_logger()
    logger.info("夸克网盘批量分享工具启动")
    
    try:
        # 创建主窗口
        root = tk.Tk()
        app = SimpleMainWindow(root)
        
        # 启动GUI
        root.mainloop()
        
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        messagebox.showerror("错误", f"程序运行出错: {e}")
    
    logger.info("夸克网盘批量分享工具退出")

if __name__ == "__main__":
    main()
