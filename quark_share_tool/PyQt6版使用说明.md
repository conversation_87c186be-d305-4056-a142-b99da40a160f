# 🌟 夸克网盘批量分享工具 v3.0 (PyQt6版)

## 🎯 全新PyQt6图形界面

### ✨ 主要特性
- **🖥️ 现代化界面**: 使用PyQt6构建的专业级图形界面
- **🎨 美观设计**: 精心设计的UI布局和配色方案
- **🌙 夜间模式支持**: 自动适配系统主题，支持手动切换深色/浅色主题 ⭐
- **⚡ 高性能**: 多线程处理，响应迅速
- **🔧 稳定可靠**: 成熟的PyQt6框架，稳定性更好
- **📱 跨平台**: 支持Windows、macOS、Linux

### 🚀 快速启动

#### 方法一：使用启动脚本（推荐）

**Windows:**
```bash
start_qt.bat
```

**macOS/Linux:**
```bash
./start_qt.sh
```

#### 方法二：手动启动

1. **创建虚拟环境**
```bash
python3 -m venv venv_qt
```

2. **激活虚拟环境**
```bash
# Windows
venv_qt\Scripts\activate

# macOS/Linux
source venv_qt/bin/activate
```

3. **安装依赖**
```bash
pip install PyQt6 openpyxl requests
```

4. **启动程序**
```bash
python main_qt.py
```

### 🎨 界面预览

```
┌─────────────────────────────────────────────────────────────────┐
│ 🌟 夸克网盘批量分享工具 v3.0 (PyQt6版)                          │
├─────────────────────────────────────────────────────────────────┤
│ 账户状态: [● 未登录] [🔑 登录] [🚪 登出]     ⏰ 2024-06-24 20:45:30 │
├─────────────────────────────────────────────────────────────────┤
│ 📁 文件管理                    │ 📊 分享结果                     │
│ ┌─────────────────────────────┐ │ ┌─────────────────────────────┐ │
│ │[🔄刷新][☑️全选][☐取消全选]  │ │ │[📄导出Excel][🗑️清空结果]   │ │
│ │[🚀批量分享]    文件:8|已选:3│ │ │           结果:5|成功:4|失败:1│ │
│ │🔍搜索: [_____________] [✖]  │ │ └─────────────────────────────┘ │
│ │                            │ │                                 │
│ │☑ 重要文档.pdf    2.1MB     │ │ ✅ 重要文档.pdf                │
│ │☐ 照片集合        -         │ │ https://pan.quark.cn/s/abc123  │
│ │☑ 项目资料.zip   50.2MB     │ │ 提取码: xy8k                   │
│ │☐ 视频教程.mp4  100.5MB     │ │                                │
│ │...                         │ │ ❌ 视频教程.mp4                │
│ └─────────────────────────────┘ │ 分享失败: 文件过大              │
└─────────────────────────────────┴─────────────────────────────────┘
│ 状态栏: ✅ 分享完成: 4/5 成功                                     │
└─────────────────────────────────────────────────────────────────┘
```

### 🎯 操作流程

#### 1️⃣ 登录账户
1. 点击 **"🔑 登录"** 按钮
2. 在弹出的对话框中输入：
   - 用户名：任意用户名（演示版本）
   - 密码：任意密码（演示版本）
3. 点击 **"登录"** 按钮
4. 登录成功后状态变为绿色 **"● 已登录: 用户名"**

#### 2️⃣ 选择文件
- **双击文件行**：切换选择状态（☐ ↔ ☑）
- **全选按钮**：一键选择所有文件
- **取消全选**：一键取消所有选择
- **搜索功能**：输入文件名进行实时过滤

#### 3️⃣ 批量分享
1. 选择要分享的文件
2. 点击 **"🚀 批量分享"** 按钮
3. 查看进度对话框显示处理进度
4. 分享完成后结果自动显示在右侧

#### 4️⃣ 导出Excel
1. 分享完成后点击 **"📄 导出Excel"** 按钮
2. 选择保存位置和文件名
3. 等待导出完成

### 🎨 界面特色

#### 🎪 **现代化设计**
- 扁平化设计风格
- 清晰的图标和按钮
- 舒适的配色方案
- 响应式布局

#### 🖱️ **交互体验**
- 按钮悬停效果
- 双击操作支持
- 拖拽调整分栏
- 实时状态更新

#### 📊 **信息展示**
- 实时文件统计
- 分享结果统计
- 操作进度显示
- 状态栏信息

### 🔧 高级功能

#### 📋 **链接复制**
- 双击分享结果行自动复制完整链接
- 状态栏显示复制成功提示

#### 🔍 **智能搜索**
- 实时文件名搜索
- 支持中英文搜索
- 一键清空搜索

#### 📈 **多线程处理**
- 批量分享使用独立线程
- 界面不会卡顿
- 可以取消正在进行的操作

#### 📊 **详细统计**
- 文件选择统计
- 分享成功率统计
- 操作时间记录

### 💡 使用技巧

#### ⌨️ **键盘操作**
- **回车键**：在登录对话框中快速登录
- **Esc键**：取消当前对话框
- **Tab键**：在界面元素间切换焦点

#### 🖱️ **鼠标操作**
- **双击文件**：切换选择状态
- **双击结果**：复制分享链接
- **拖拽分栏**：调整左右区域大小

### 📱 系统要求

#### 💻 **操作系统**
- ✅ Windows 10/11
- ✅ macOS 10.14+
- ✅ Linux (Ubuntu 18.04+)

#### 🐍 **Python版本**
- ✅ Python 3.7+
- ✅ 自动虚拟环境管理
- ✅ 自动依赖安装

#### 📦 **依赖包**
- PyQt6 >= 6.4.0
- openpyxl >= 3.1.0
- requests >= 2.31.0

### 🆕 v3.0 新特性

1. **🎨 全新PyQt6界面**: 专业级图形界面框架
2. **⚡ 多线程处理**: 批量操作不阻塞界面
3. **🔧 虚拟环境**: 自动管理Python环境和依赖
4. **📱 响应式设计**: 支持窗口大小调整
5. **🎪 现代化交互**: 更好的用户体验
6. **📊 实时统计**: 详细的操作统计信息
7. **🔍 智能搜索**: 实时文件过滤功能
8. **📋 一键复制**: 双击复制分享链接

### 🐛 故障排除

#### ❓ **程序无法启动**
1. 检查Python版本是否为3.7+
2. 使用启动脚本自动安装依赖
3. 检查是否有权限创建虚拟环境

#### ❓ **登录无效果**
1. 确认程序已完全启动
2. 查看控制台是否有错误信息
3. 重启程序重试

#### ❓ **界面显示异常**
1. 检查屏幕分辨率设置
2. 尝试调整窗口大小
3. 重启程序恢复默认布局

### 🎉 **立即体验**

使用启动脚本一键启动，享受专业级的PyQt6图形界面！

```bash
# Windows
start_qt.bat

# macOS/Linux
./start_qt.sh
```

---

**版本**: v3.0 (PyQt6版)  
**更新日期**: 2024-06-24  
**技术栈**: Python + PyQt6 + 多线程
