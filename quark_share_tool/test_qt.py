#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt6测试程序 - 检查基本功能
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QPushButton, QLabel
from PyQt6.QtCore import Qt

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PyQt6测试窗口 - 深色主题")
        self.setGeometry(100, 100, 500, 400)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加标签
        label = QLabel("🌙 PyQt6深色主题测试")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label)
        
        # 添加说明
        info_label = QLabel("如果您能看到这个深色界面，说明夜间模式适配成功！")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(info_label)
        
        # 添加按钮
        button = QPushButton("✅ 测试按钮 - 点击我")
        button.clicked.connect(self.on_button_click)
        layout.addWidget(button)
        
        # 添加退出按钮
        exit_button = QPushButton("❌ 退出程序")
        exit_button.clicked.connect(self.close)
        layout.addWidget(exit_button)
        
        # 应用深色主题样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                padding: 20px;
                background-color: #353535;
                border-radius: 8px;
                margin: 10px;
            }
            QPushButton {
                background-color: #404040;
                border: 1px solid #555;
                padding: 15px 25px;
                border-radius: 6px;
                color: #ffffff;
                font-size: 14px;
                font-weight: bold;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #505050;
                border: 1px solid #777;
            }
            QPushButton:pressed {
                background-color: #606060;
            }
        """)
        
        # 创建状态栏
        self.statusBar().showMessage("🟢 PyQt6深色主题测试程序就绪")
        self.statusBar().setStyleSheet("""
            QStatusBar {
                background-color: #353535;
                color: #ffffff;
                border-top: 1px solid #555;
                padding: 5px;
            }
        """)
    
    def on_button_click(self):
        print("✅ 按钮被点击了！深色主题工作正常。")
        self.statusBar().showMessage("✅ 按钮点击成功！深色主题显示正常。")

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("PyQt6深色主题测试")
    
    window = TestWindow()
    window.show()
    
    print("🌙 PyQt6深色主题测试窗口已启动")
    print("如果界面显示正常，说明夜间模式适配成功！")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
