#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
夸克网盘批量分享工具 - PyQt6版本
主程序入口
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui.qt_main_window import QuarkShareMainWindow
from utils.logger import setup_logger

def main():
    """主函数"""
    # 设置日志
    logger = setup_logger()
    logger.info("PyQt6版夸克网盘批量分享工具启动")
    
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 设置应用程序属性
        app.setApplicationName("夸克网盘批量分享工具")
        app.setApplicationVersion("3.0")
        app.setOrganizationName("QuarkShare")
        
        # 设置高DPI支持（PyQt6中某些属性可能不可用）
        try:
            app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
        except AttributeError:
            pass  # 在某些PyQt6版本中可能不可用
        
        # 创建主窗口
        window = QuarkShareMainWindow()
        window.show()
        
        # 启动事件循环
        sys.exit(app.exec())
        
    except ImportError as e:
        error_msg = f"缺少必要的依赖包: {e}\n\n请运行以下命令安装依赖:\npip install PyQt6"
        print(error_msg)
        
        # 尝试显示图形错误对话框
        try:
            app = QApplication(sys.argv)
            QMessageBox.critical(None, "依赖错误", error_msg)
        except:
            pass
        
        logger.error(f"依赖错误: {e}")
        sys.exit(1)
        
    except Exception as e:
        error_msg = f"程序运行出错: {e}"
        logger.error(error_msg)
        
        # 尝试显示图形错误对话框
        try:
            QMessageBox.critical(None, "错误", error_msg)
        except:
            print(error_msg)
        
        sys.exit(1)

if __name__ == "__main__":
    main()
