#!/bin/bash

echo "🌟 夸克网盘批量分享工具 PyQt6版"
echo "=================================="

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误：未找到Python3，请先安装Python 3.7+"
    exit 1
fi

echo "✅ 使用Python: $(which python3)"

# 检查虚拟环境是否存在
if [ ! -d "venv_qt" ]; then
    echo "📦 创建虚拟环境..."
    python3 -m venv venv_qt
    if [ $? -ne 0 ]; then
        echo "❌ 错误：创建虚拟环境失败"
        exit 1
    fi
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source venv_qt/bin/activate

# 检查依赖是否安装
echo "📋 检查依赖包..."
if ! python -c "import PyQt6" &> /dev/null; then
    echo "📦 正在安装依赖包..."
    pip install PyQt6 openpyxl requests
    if [ $? -ne 0 ]; then
        echo "❌ 错误：依赖包安装失败"
        exit 1
    fi
fi

echo "✅ 依赖检查完成"

# 启动程序
echo "🚀 启动程序..."
python main_qt.py

echo "👋 程序已退出"
