#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
夸克网盘API接口模拟
由于无法直接访问夸克网盘的真实API，这里提供模拟接口用于演示
"""

import time
import random
import string
from datetime import datetime
from typing import List, Dict, Optional
import requests
import json

class QuarkFile:
    """夸克网盘文件对象"""
    def __init__(self, file_id: str, name: str, size: int, file_type: str, path: str):
        self.file_id = file_id
        self.name = name
        self.size = size
        self.file_type = file_type  # 'file' or 'folder'
        self.path = path
        self.created_time = datetime.now()
        self.share_link = None
        self.share_code = None

class QuarkAPI:
    """夸克网盘API模拟类"""
    
    def __init__(self):
        self.is_logged_in = False
        self.username = ""
        self.session_token = ""
        self.files = []
        self._init_demo_files()
    
    def _init_demo_files(self):
        """初始化演示文件"""
        demo_files = [
            # 根目录文件
            QuarkFile("f001", "重要文档.pdf", 1024*1024*2, "file", "/"),
            QuarkFile("f002", "项目资料.zip", 1024*1024*50, "file", "/"),
            QuarkFile("f003", "照片集合", 0, "folder", "/"),
            QuarkFile("f004", "视频教程.mp4", 1024*1024*100, "file", "/"),
            QuarkFile("f005", "工作报告.docx", 1024*512, "file", "/"),
            QuarkFile("f006", "数据备份.rar", 1024*1024*200, "file", "/"),
            QuarkFile("f007", "音乐收藏", 0, "folder", "/"),
            QuarkFile("f008", "软件安装包.exe", 1024*1024*30, "file", "/"),

            # 照片集合文件夹内的文件
            QuarkFile("f009", "风景照片1.jpg", 1024*1024*3, "file", "/照片集合/"),
            QuarkFile("f010", "风景照片2.jpg", 1024*1024*4, "file", "/照片集合/"),
            QuarkFile("f011", "人物照片", 0, "folder", "/照片集合/"),
            QuarkFile("f012", "旅游照片.zip", 1024*1024*20, "file", "/照片集合/"),

            # 音乐收藏文件夹内的文件
            QuarkFile("f013", "流行音乐", 0, "folder", "/音乐收藏/"),
            QuarkFile("f014", "古典音乐", 0, "folder", "/音乐收藏/"),
            QuarkFile("f015", "我的歌单.m3u", 1024*10, "file", "/音乐收藏/"),

            # 人物照片子文件夹
            QuarkFile("f016", "家庭照片.jpg", 1024*1024*2, "file", "/照片集合/人物照片/"),
            QuarkFile("f017", "朋友聚会.jpg", 1024*1024*3, "file", "/照片集合/人物照片/"),

            # 流行音乐子文件夹
            QuarkFile("f018", "热门单曲.mp3", 1024*1024*5, "file", "/音乐收藏/流行音乐/"),
            QuarkFile("f019", "经典老歌.mp3", 1024*1024*4, "file", "/音乐收藏/流行音乐/"),

            # 古典音乐子文件夹
            QuarkFile("f020", "贝多芬交响曲.mp3", 1024*1024*8, "file", "/音乐收藏/古典音乐/"),
            QuarkFile("f021", "莫扎特小夜曲.mp3", 1024*1024*6, "file", "/音乐收藏/古典音乐/"),
        ]
        self.files = demo_files
    
    def login(self, username: str, password: str) -> bool:
        """
        模拟登录
        Args:
            username: 用户名
            password: 密码
        Returns:
            bool: 登录是否成功
        """
        # 模拟网络延迟
        time.sleep(1)
        
        # 简单的模拟验证（实际应用中需要真实的API调用）
        if username and password:
            self.is_logged_in = True
            self.username = username
            self.session_token = self._generate_token()
            return True
        return False
    
    def logout(self):
        """登出"""
        self.is_logged_in = False
        self.username = ""
        self.session_token = ""
    
    def get_files(self, path: str = "/") -> List[QuarkFile]:
        """
        获取文件列表
        Args:
            path: 目录路径
        Returns:
            List[QuarkFile]: 文件列表
        """
        if not self.is_logged_in:
            raise Exception("请先登录")

        # 模拟网络延迟
        time.sleep(0.5)

        # 根据路径过滤文件
        filtered_files = []
        for file_obj in self.files:
            if file_obj.path == path:
                filtered_files.append(file_obj)

        return filtered_files
    
    def create_share_link(self, file_id: str, expire_days: int = 7, 
                         need_password: bool = True) -> Dict[str, str]:
        """
        创建分享链接
        Args:
            file_id: 文件ID
            expire_days: 过期天数
            need_password: 是否需要密码
        Returns:
            Dict: 包含分享链接和密码的字典
        """
        if not self.is_logged_in:
            raise Exception("请先登录")
        
        # 查找文件
        file_obj = None
        for f in self.files:
            if f.file_id == file_id:
                file_obj = f
                break
        
        if not file_obj:
            raise Exception(f"文件不存在: {file_id}")
        
        # 模拟网络延迟
        time.sleep(random.uniform(0.5, 2.0))
        
        # 生成模拟的分享链接和密码
        share_link = f"https://pan.quark.cn/s/{self._generate_share_id()}"
        share_code = self._generate_share_code() if need_password else ""
        
        # 保存到文件对象
        file_obj.share_link = share_link
        file_obj.share_code = share_code
        
        return {
            "share_link": share_link,
            "share_code": share_code,
            "expire_time": self._get_expire_time(expire_days)
        }
    
    def batch_create_share_links(self, file_ids: List[str], 
                               expire_days: int = 7, 
                               need_password: bool = True,
                               progress_callback=None) -> List[Dict]:
        """
        批量创建分享链接
        Args:
            file_ids: 文件ID列表
            expire_days: 过期天数
            need_password: 是否需要密码
            progress_callback: 进度回调函数
        Returns:
            List[Dict]: 分享结果列表
        """
        results = []
        total = len(file_ids)
        
        for i, file_id in enumerate(file_ids):
            try:
                # 创建分享链接
                share_info = self.create_share_link(file_id, expire_days, need_password)
                
                # 获取文件信息
                file_obj = next((f for f in self.files if f.file_id == file_id), None)
                
                result = {
                    "file_id": file_id,
                    "file_name": file_obj.name if file_obj else "未知文件",
                    "file_size": file_obj.size if file_obj else 0,
                    "share_link": share_info["share_link"],
                    "share_code": share_info["share_code"],
                    "expire_time": share_info["expire_time"],
                    "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "status": "成功"
                }
                
            except Exception as e:
                result = {
                    "file_id": file_id,
                    "file_name": "未知文件",
                    "file_size": 0,
                    "share_link": "",
                    "share_code": "",
                    "expire_time": "",
                    "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "status": f"失败: {str(e)}"
                }
            
            results.append(result)
            
            # 调用进度回调
            if progress_callback:
                progress_callback(i + 1, total, result["file_name"])
        
        return results
    
    def _generate_token(self) -> str:
        """生成会话令牌"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=32))
    
    def _generate_share_id(self) -> str:
        """生成分享ID"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=12))
    
    def _generate_share_code(self) -> str:
        """生成分享密码"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=4))
    
    def _get_expire_time(self, days: int) -> str:
        """获取过期时间"""
        from datetime import timedelta
        expire_time = datetime.now() + timedelta(days=days)
        return expire_time.strftime("%Y-%m-%d %H:%M:%S")
    
    def get_file_by_id(self, file_id: str) -> Optional[QuarkFile]:
        """根据ID获取文件"""
        for file_obj in self.files:
            if file_obj.file_id == file_id:
                return file_obj
        return None
