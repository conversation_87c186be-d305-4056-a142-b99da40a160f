#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API工厂
根据配置自动选择使用演示API还是真实API
"""

from config import config
from utils.logger import setup_logger

def create_quark_api():
    """
    创建夸克网盘API实例
    根据配置自动选择API模式
    Returns:
        QuarkAPI实例
    """
    logger = setup_logger()

    api_mode = config.get("mode.api_type", "web")  # web/api/demo

    if config.is_demo_mode() or api_mode == "demo":
        logger.info("使用演示模式API")
        from core.quark_api import QuarkAPI
        return QuarkAPI()
    elif api_mode == "web":
        logger.info("使用网页端API")
        from core.quark_web_api import QuarkWebAPI
        return QuarkWebAPI()
    else:
        logger.info("使用原生API")
        from core.quark_api_real import QuarkAPIReal
        return QuarkAPIReal()

def get_api_info():
    """
    获取当前API模式信息
    Returns:
        Dict: API信息
    """
    api_mode = config.get("mode.api_type", "web")

    if config.is_demo_mode() or api_mode == "demo":
        return {
            "mode": "演示模式",
            "description": "使用模拟数据，适合功能测试和演示",
            "features": [
                "模拟文件列表",
                "模拟分享链接",
                "无需真实账户",
                "快速响应"
            ],
            "limitations": [
                "无法访问真实文件",
                "分享链接为模拟数据",
                "仅用于演示目的"
            ]
        }
    elif api_mode == "web":
        return {
            "mode": "网页端模式",
            "description": "使用浏览器模拟操作，速度快，稳定性好",
            "features": [
                "真实文件访问",
                "真实分享链接",
                "支持扫码登录",
                "保持登录状态",
                "速度较快",
                "稳定性好"
            ],
            "requirements": [
                "需要真实夸克网盘账户",
                "需要网络连接",
                "需要Chrome浏览器"
            ]
        }
    else:
        return {
            "mode": "原生API模式",
            "description": "直接调用夸克网盘API接口",
            "features": [
                "真实文件访问",
                "真实分享链接",
                "完整功能支持",
                "数据持久化"
            ],
            "requirements": [
                "需要真实夸克网盘账户",
                "需要网络连接",
                "需要API权限配置"
            ]
        }

def switch_mode(demo_mode: bool):
    """
    切换API模式
    Args:
        demo_mode: True=演示模式, False=正式模式
    """
    logger = setup_logger()
    
    old_mode = "演示模式" if config.is_demo_mode() else "正式模式"
    new_mode = "演示模式" if demo_mode else "正式模式"
    
    config.set_demo_mode(demo_mode)
    config.save_user_config()
    
    logger.info(f"API模式已从 {old_mode} 切换到 {new_mode}")
    
    return {
        "success": True,
        "old_mode": old_mode,
        "new_mode": new_mode,
        "message": f"已切换到{new_mode}，重启程序后生效"
    }
