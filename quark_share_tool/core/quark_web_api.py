#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
夸克网盘网页端API
使用Selenium模拟浏览器操作，速度更快，更稳定
"""

import time
import json
import os
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager

class QuarkFile:
    """夸克网盘文件对象"""
    def __init__(self, file_id: str, name: str, size: int, file_type: str, path: str, 
                 created_time: datetime = None, modified_time: datetime = None):
        self.file_id = file_id
        self.name = name
        self.size = size
        self.file_type = file_type  # 'file' or 'folder'
        self.path = path
        self.created_time = created_time or datetime.now()
        self.modified_time = modified_time or datetime.now()
        self.share_link = None
        self.share_code = None

class QuarkWebAPI:
    """夸克网盘网页端API类"""
    
    def __init__(self):
        self.is_logged_in = False
        self.username = ""
        self.driver = None
        self.session = requests.Session()
        self.cookies = {}
        
        # 网页端URL
        self.login_url = "https://pan.quark.cn/account/login"
        self.drive_url = "https://pan.quark.cn/list"
        self.api_base = "https://drive-pc.quark.cn"
        
        self.current_folder_id = "0"  # 当前文件夹ID
        self.folder_path_map = {"/": "0"}  # 路径到文件夹ID的映射
    
    def _setup_driver(self):
        """设置Chrome浏览器驱动"""
        if self.driver:
            return
        
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1200,800")
            
            # 设置用户数据目录，保持登录状态
            user_data_dir = os.path.expanduser("~/.quark_browser_data")
            chrome_options.add_argument(f"--user-data-dir={user_data_dir}")
            
            # 自动下载ChromeDriver
            service = Service(ChromeDriverManager().install())
            
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.implicitly_wait(10)
            
        except Exception as e:
            raise Exception(f"浏览器初始化失败: {e}")
    
    def login(self, username: str = None, password: str = None) -> bool:
        """
        网页端登录
        Args:
            username: 用户名（可选，支持扫码登录）
            password: 密码（可选）
        Returns:
            bool: 登录是否成功
        """
        try:
            self._setup_driver()
            
            # 访问登录页面
            self.driver.get(self.login_url)
            time.sleep(3)
            
            # 检查是否已经登录
            if self._check_login_status():
                self.is_logged_in = True
                self.username = username or "已登录用户"
                self._extract_cookies()
                return True
            
            if username and password:
                # 用户名密码登录
                return self._login_with_password(username, password)
            else:
                # 扫码登录或手动登录
                return self._login_manual()
                
        except Exception as e:
            raise Exception(f"登录失败: {e}")
    
    def _login_with_password(self, username: str, password: str) -> bool:
        """用户名密码登录"""
        try:
            # 等待登录表单加载
            wait = WebDriverWait(self.driver, 20)
            
            # 查找用户名输入框
            username_input = wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='text'], input[placeholder*='手机'], input[placeholder*='邮箱']"))
            )
            username_input.clear()
            username_input.send_keys(username)
            
            # 查找密码输入框
            password_input = self.driver.find_element(By.CSS_SELECTOR, "input[type='password']")
            password_input.clear()
            password_input.send_keys(password)
            
            # 查找登录按钮
            login_button = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit'], .login-btn, .submit-btn")
            login_button.click()
            
            # 等待登录完成
            time.sleep(5)
            
            # 检查登录状态
            if self._check_login_status():
                self.is_logged_in = True
                self.username = username
                self._extract_cookies()
                return True
            else:
                return False
                
        except Exception as e:
            raise Exception(f"用户名密码登录失败: {e}")
    
    def _login_manual(self) -> bool:
        """手动登录（扫码或其他方式）"""
        try:
            from PyQt6.QtWidgets import QMessageBox, QApplication
            
            # 显示提示对话框
            app = QApplication.instance()
            if app:
                msg = QMessageBox()
                msg.setWindowTitle("网页登录")
                msg.setText("请在打开的浏览器窗口中完成登录操作")
                msg.setInformativeText("支持扫码登录、短信验证等方式\n登录完成后点击'确定'继续")
                msg.setStandardButtons(QMessageBox.StandardButton.Ok | QMessageBox.StandardButton.Cancel)
                
                result = msg.exec()
                if result != QMessageBox.StandardButton.Ok:
                    return False
            
            # 检查登录状态
            if self._check_login_status():
                self.is_logged_in = True
                self.username = "网页登录用户"
                self._extract_cookies()
                return True
            else:
                return False
                
        except Exception as e:
            raise Exception(f"手动登录失败: {e}")
    
    def _check_login_status(self) -> bool:
        """检查登录状态"""
        try:
            # 尝试访问网盘主页
            self.driver.get(self.drive_url)
            time.sleep(3)
            
            # 检查是否存在登录后的元素
            current_url = self.driver.current_url
            page_source = self.driver.page_source
            
            # 如果URL包含登录页面，说明未登录
            if "login" in current_url or "登录" in page_source:
                return False
            
            # 如果能找到文件列表相关元素，说明已登录
            if "文件" in page_source or "file" in page_source.lower():
                return True
            
            return False
            
        except Exception:
            return False
    
    def _extract_cookies(self):
        """提取浏览器Cookie"""
        try:
            cookies = self.driver.get_cookies()
            self.cookies = {}
            
            for cookie in cookies:
                self.cookies[cookie['name']] = cookie['value']
            
            # 更新requests会话的Cookie
            self.session.cookies.update(self.cookies)
            
            # 设置请求头
            self.session.headers.update({
                'User-Agent': self.driver.execute_script("return navigator.userAgent;"),
                'Referer': 'https://pan.quark.cn/',
                'Origin': 'https://pan.quark.cn'
            })
            
        except Exception as e:
            print(f"提取Cookie失败: {e}")
    
    def logout(self):
        """登出"""
        try:
            self.is_logged_in = False
            self.username = ""
            self.cookies = {}
            
            if self.driver:
                self.driver.quit()
                self.driver = None
                
        except Exception:
            pass
    
    def get_files(self, path: str = "/", page: int = 1, limit: int = 100) -> List[QuarkFile]:
        """
        获取文件列表
        Args:
            path: 目录路径
            page: 页码
            limit: 每页数量
        Returns:
            List[QuarkFile]: 文件列表
        """
        if not self.is_logged_in:
            raise Exception("请先登录")
        
        try:
            # 获取文件夹ID
            folder_id = self._get_folder_id_by_path(path)
            
            # 构建API请求
            api_url = f"{self.api_base}/1/clouddrive/file/sort"
            
            params = {
                "pr": "ucpro",
                "fr": "pc",
                "pdir_fid": folder_id,
                "page": page,
                "size": limit,
                "order": "file_type",
                "asc": 0,
                "_ch": "home",
                "_t": int(time.time() * 1000)
            }
            
            response = self.session.get(api_url, params=params, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get("code") == 0:
                    files_data = result["data"]["list"]
                    files = []
                    
                    for file_data in files_data:
                        file_obj = QuarkFile(
                            file_id=file_data["fid"],
                            name=file_data["file_name"],
                            size=int(file_data.get("size", 0)),
                            file_type="folder" if file_data["dir"] else "file",
                            path=path,
                            created_time=self._parse_time(file_data.get("created_at")),
                            modified_time=self._parse_time(file_data.get("updated_at"))
                        )
                        files.append(file_obj)
                    
                    return files
                else:
                    raise Exception(f"获取文件列表失败: {result.get('message', '未知错误')}")
            else:
                raise Exception(f"网络请求失败: HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"网络连接失败: {str(e)}")
        except Exception as e:
            raise Exception(f"获取文件列表出错: {str(e)}")
    
    def create_share_link(self, file_id: str, expire_days: int = 7, 
                         need_password: bool = True) -> Dict[str, str]:
        """
        创建分享链接
        Args:
            file_id: 文件ID
            expire_days: 过期天数
            need_password: 是否需要密码
        Returns:
            Dict: 包含分享链接和密码的字典
        """
        if not self.is_logged_in:
            raise Exception("请先登录")
        
        try:
            share_url = f"{self.api_base}/1/clouddrive/share"
            
            # 计算过期时间
            expire_time = int((datetime.now() + timedelta(days=expire_days)).timestamp())
            
            share_data = {
                "fid_list": [file_id],
                "title": "",
                "url_type": 1,
                "expired_type": 1 if expire_days > 0 else 0,
                "expired_at": expire_time,
                "passcode": "" if not need_password else self._generate_share_code()
            }
            
            response = self.session.post(share_url, json=share_data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get("code") == 0:
                    share_data = result["data"]
                    return {
                        "share_link": share_data["share_url"],
                        "share_code": share_data.get("passcode", ""),
                        "expire_time": datetime.fromtimestamp(expire_time).strftime("%Y-%m-%d %H:%M:%S")
                    }
                else:
                    raise Exception(f"创建分享链接失败: {result.get('message', '未知错误')}")
            else:
                raise Exception(f"网络请求失败: HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"网络连接失败: {str(e)}")
        except Exception as e:
            raise Exception(f"创建分享链接出错: {str(e)}")
    
    def batch_create_share_links(self, file_ids: List[str], 
                               expire_days: int = 7, 
                               need_password: bool = True,
                               progress_callback=None) -> List[Dict]:
        """
        批量创建分享链接
        Args:
            file_ids: 文件ID列表
            expire_days: 过期天数
            need_password: 是否需要密码
            progress_callback: 进度回调函数
        Returns:
            List[Dict]: 分享结果列表
        """
        results = []
        total = len(file_ids)
        
        for i, file_id in enumerate(file_ids):
            try:
                # 获取文件信息
                file_info = self._get_file_info(file_id)
                
                # 创建分享链接
                share_info = self.create_share_link(file_id, expire_days, need_password)
                
                result = {
                    "file_id": file_id,
                    "file_name": file_info.get("name", "未知文件"),
                    "file_size": file_info.get("size", 0),
                    "share_link": share_info["share_link"],
                    "share_code": share_info["share_code"],
                    "expire_time": share_info["expire_time"],
                    "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "status": "成功"
                }
                
            except Exception as e:
                result = {
                    "file_id": file_id,
                    "file_name": "未知文件",
                    "file_size": 0,
                    "share_link": "",
                    "share_code": "",
                    "expire_time": "",
                    "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "status": f"失败: {str(e)}"
                }
            
            results.append(result)
            
            # 调用进度回调
            if progress_callback:
                progress_callback(i + 1, total, result["file_name"])
            
            # 避免请求过于频繁
            time.sleep(0.3)
        
        return results
    
    def _get_folder_id_by_path(self, path: str) -> str:
        """根据路径获取文件夹ID"""
        if path in self.folder_path_map:
            return self.folder_path_map[path]
        
        # 如果路径不在映射中，返回根目录ID
        return "0"
    
    def _get_file_info(self, file_id: str) -> Dict:
        """获取文件信息"""
        try:
            info_url = f"{self.api_base}/1/clouddrive/file/info"
            params = {"fid": file_id}
            
            response = self.session.get(info_url, params=params, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    return {
                        "name": result["data"]["file_name"],
                        "size": result["data"].get("size", 0)
                    }
            
            return {"name": "未知文件", "size": 0}
            
        except:
            return {"name": "未知文件", "size": 0}
    
    def _parse_time(self, time_str: str) -> datetime:
        """解析时间字符串"""
        try:
            if time_str:
                return datetime.fromtimestamp(int(time_str))
        except:
            pass
        return datetime.now()
    
    def _generate_share_code(self) -> str:
        """生成分享密码"""
        import random
        import string
        return ''.join(random.choices(string.ascii_letters + string.digits, k=4))
