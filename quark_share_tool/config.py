#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
控制程序运行模式和参数
"""

import os
from typing import Dict, Any

class Config:
    """配置管理类"""
    
    def __init__(self):
        self.config = self._load_default_config()
        self._load_user_config()
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            # 运行模式配置
            "mode": {
                "demo_mode": False,  # False=正式模式, True=演示模式
                "api_type": "web",   # web=网页端API, api=原生API, demo=演示模式
                "debug": False,      # 调试模式
                "log_level": "INFO"  # 日志级别
            },
            
            # API配置
            "api": {
                "base_url": "https://drive-pc.quark.cn",
                "timeout": 30,
                "retry_times": 3,
                "retry_delay": 1
            },
            
            # 分享配置
            "share": {
                "default_expire_days": 7,    # 默认过期天数
                "default_need_password": True, # 默认是否需要密码
                "batch_delay": 0.5,          # 批量操作间隔(秒)
                "max_batch_size": 50         # 最大批量数量
            },
            
            # 界面配置
            "ui": {
                "theme": "auto",             # auto/light/dark
                "window_size": "1200x800",   # 窗口大小
                "language": "zh_CN"          # 语言
            },
            
            # 导出配置
            "export": {
                "default_format": "xlsx",    # 默认导出格式
                "include_statistics": True,  # 是否包含统计信息
                "auto_open": False          # 导出后是否自动打开
            }
        }
    
    def _load_user_config(self):
        """加载用户配置文件"""
        config_file = "user_config.json"
        
        if os.path.exists(config_file):
            try:
                import json
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    self._merge_config(user_config)
            except Exception as e:
                print(f"加载用户配置失败: {e}")
    
    def _merge_config(self, user_config: Dict[str, Any]):
        """合并用户配置"""
        def merge_dict(base: Dict, update: Dict):
            for key, value in update.items():
                if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                    merge_dict(base[key], value)
                else:
                    base[key] = value
        
        merge_dict(self.config, user_config)
    
    def get(self, key_path: str, default=None):
        """
        获取配置值
        Args:
            key_path: 配置路径，如 "mode.demo_mode"
            default: 默认值
        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = self.config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    
    def set(self, key_path: str, value):
        """
        设置配置值
        Args:
            key_path: 配置路径
            value: 配置值
        """
        keys = key_path.split('.')
        config = self.config
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
    
    def save_user_config(self):
        """保存用户配置到文件"""
        try:
            import json
            with open("user_config.json", 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存用户配置失败: {e}")
    
    def is_demo_mode(self) -> bool:
        """是否为演示模式"""
        return self.get("mode.demo_mode", False)
    
    def set_demo_mode(self, demo_mode: bool):
        """设置演示模式"""
        self.set("mode.demo_mode", demo_mode)
    
    def get_api_config(self) -> Dict[str, Any]:
        """获取API配置"""
        return self.get("api", {})
    
    def get_share_config(self) -> Dict[str, Any]:
        """获取分享配置"""
        return self.get("share", {})
    
    def get_ui_config(self) -> Dict[str, Any]:
        """获取界面配置"""
        return self.get("ui", {})

# 全局配置实例
config = Config()

# 环境变量覆盖
if os.getenv("QUARK_DEMO_MODE", "").lower() in ("true", "1", "yes"):
    config.set_demo_mode(True)

if os.getenv("QUARK_DEBUG", "").lower() in ("true", "1", "yes"):
    config.set("mode.debug", True)
