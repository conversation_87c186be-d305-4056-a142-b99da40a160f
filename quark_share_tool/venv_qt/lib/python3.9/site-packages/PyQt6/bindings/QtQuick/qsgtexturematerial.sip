// qsgtexturematerial.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSGOpaqueTextureMaterial : public QSGMaterial
{
%TypeHeaderCode
#include <qsgtexturematerial.h>
%End

public:
    QSGOpaqueTextureMaterial();
    virtual QSGMaterialType *type() const;
    virtual QSGMaterialShader *createShader(QSGRendererInterface::RenderMode renderMode) const /Factory/;
    virtual int compare(const QSGMaterial *other) const;
    void setTexture(QSGTexture *texture);
    QSGTexture *texture() const;
    void setMipmapFiltering(QSGTexture::Filtering filtering);
    QSGTexture::Filtering mipmapFiltering() const;
    void setFiltering(QSGTexture::Filtering filtering);
    QSGTexture::Filtering filtering() const;
    void setHorizontalWrapMode(QSGTexture::WrapMode mode);
    QSGTexture::WrapMode horizontalWrapMode() const;
    void setVerticalWrapMode(QSGTexture::WrapMode mode);
    QSGTexture::WrapMode verticalWrapMode() const;
    void setAnisotropyLevel(QSGTexture::AnisotropyLevel level);
    QSGTexture::AnisotropyLevel anisotropyLevel() const;
};

class QSGTextureMaterial : public QSGOpaqueTextureMaterial
{
%TypeHeaderCode
#include <qsgtexturematerial.h>
%End

public:
    virtual QSGMaterialType *type() const;
    virtual QSGMaterialShader *createShader(QSGRendererInterface::RenderMode renderMode) const /Factory/;
};
