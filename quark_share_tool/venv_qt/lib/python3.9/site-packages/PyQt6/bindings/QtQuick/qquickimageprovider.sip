// qquickimageprovider.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQuickTextureFactory : public QObject
{
%TypeHeaderCode
#include <qquickimageprovider.h>
%End

public:
    QQuickTextureFactory();
    virtual ~QQuickTextureFactory();
    virtual QSGTexture *createTexture(QQuickWindow *window) const = 0 /Factory/;
    virtual QSize textureSize() const = 0;
    virtual int textureByteCount() const = 0;
    virtual QImage image() const;
    static QQuickTextureFactory *textureFactoryForImage(const QImage &image) /Factory/;
};

class QQuickImageProvider : public QQmlImageProviderBase
{
%TypeHeaderCode
#include <qquickimageprovider.h>
%End

public:
    QQuickImageProvider(QQmlImageProviderBase::ImageType type, QQmlImageProviderBase::Flags flags = QQmlImageProviderBase::Flags());
    virtual ~QQuickImageProvider();
    virtual QQmlImageProviderBase::ImageType imageType() const;
    virtual QQmlImageProviderBase::Flags flags() const;
    virtual QImage requestImage(const QString &id, QSize *size /Out/, const QSize &requestedSize);
    virtual QPixmap requestPixmap(const QString &id, QSize *size /Out/, const QSize &requestedSize);
    virtual QQuickTextureFactory *requestTexture(const QString &id, QSize *size /Out/, const QSize &requestedSize) /Factory/;
};

class QQuickImageResponse : public QObject
{
%TypeHeaderCode
#include <qquickimageprovider.h>
%End

public:
    QQuickImageResponse();
    virtual ~QQuickImageResponse();
    virtual QQuickTextureFactory *textureFactory() const = 0 /Factory/;
    virtual QString errorString() const;

public slots:
    virtual void cancel();

signals:
    void finished();
};

class QQuickAsyncImageProvider : public QQuickImageProvider
{
%TypeHeaderCode
#include <qquickimageprovider.h>
%End

public:
    QQuickAsyncImageProvider();
    virtual ~QQuickAsyncImageProvider();
    virtual QQuickImageResponse *requestImageResponse(const QString &id, const QSize &requestedSize) = 0 /Factory/;
};
