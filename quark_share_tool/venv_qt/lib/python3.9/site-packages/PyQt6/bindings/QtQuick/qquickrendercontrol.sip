// qquickrendercontrol.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQuickRenderControl : public QObject
{
%TypeHeaderCode
#include <qquickrendercontrol.h>
%End

public:
    explicit QQuickRenderControl(QObject *parent /TransferThis/ = 0);
    virtual ~QQuickRenderControl();
    bool initialize();
    void invalidate();
    void polishItems();
    void render();
    bool sync();
    static QWindow *renderWindowFor(QQuickWindow *win, QPoint *offset = 0);
    virtual QWindow *renderWindow(QPoint *offset);
    void prepareThread(QThread *targetThread);

signals:
    void renderRequested();
    void sceneChanged();

public:
    void setSamples(int sampleCount);
    int samples() const;
    void beginFrame();
    void endFrame();
    QQuickWindow *window() const;
};
