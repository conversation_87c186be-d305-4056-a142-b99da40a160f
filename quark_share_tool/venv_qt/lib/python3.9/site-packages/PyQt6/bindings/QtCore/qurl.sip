// qurl.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QUrl
{
%TypeHeaderCode
#include <qurl.h>
%End

%TypeCode
#include <QHash>
%End

public:
    enum ParsingMode
    {
        TolerantMode,
        StrictMode,
        DecodedMode,
    };

    QUrl();
    QUrl(const QString &url, QUrl::ParsingMode mode = QUrl::TolerantMode);
    QUrl(const QUrl &copy);
    ~QUrl();
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        PyObject *uni = qpycore_PyObject_FromQString(sipCpp->toString());
        
        if (uni)
        {
            sipRes = PyUnicode_FromFormat("PyQt6.QtCore.QUrl(%R)", uni);
        
            Py_DECREF(uni);
        }
%End

    enum UrlFormattingOption /BaseType=IntFlag/
    {
        None,
        RemoveScheme,
        RemovePassword,
        RemoveUserInfo,
        RemovePort,
        RemoveAuthority,
        RemovePath,
        RemoveQuery,
        RemoveFragment,
        PreferLocalFile,
        StripTrailingSlash,
        RemoveFilename,
        NormalizePathSegments,
    };

    typedef QFlags<QUrl::UrlFormattingOption> FormattingOptions;

    enum ComponentFormattingOption /BaseType=IntFlag/
    {
        PrettyDecoded,
        EncodeSpaces,
        EncodeUnicode,
        EncodeDelimiters,
        EncodeReserved,
        DecodeReserved,
        FullyEncoded,
        FullyDecoded,
    };

    typedef QFlags<QUrl::ComponentFormattingOption> ComponentFormattingOptions;
    QString url(QUrl::FormattingOptions options = QUrl::FormattingOptions(QUrl::PrettyDecoded)) const;
    void setUrl(const QString &url, QUrl::ParsingMode mode = QUrl::TolerantMode);
    bool isValid() const;
    bool isEmpty() const;
    void clear();
    void setScheme(const QString &scheme);
    QString scheme() const;
    void setAuthority(const QString &authority, QUrl::ParsingMode mode = QUrl::TolerantMode);
    QString authority(QUrl::ComponentFormattingOptions options = QUrl::PrettyDecoded) const;
    void setUserInfo(const QString &userInfo, QUrl::ParsingMode mode = QUrl::TolerantMode);
    QString userInfo(QUrl::ComponentFormattingOptions options = QUrl::PrettyDecoded) const;
    void setUserName(const QString &userName, QUrl::ParsingMode mode = QUrl::DecodedMode);
    QString userName(QUrl::ComponentFormattingOptions options = QUrl::FullyDecoded) const;
    void setPassword(const QString &password, QUrl::ParsingMode mode = QUrl::DecodedMode);
    QString password(QUrl::ComponentFormattingOptions options = QUrl::FullyDecoded) const;
    void setHost(const QString &host, QUrl::ParsingMode mode = QUrl::DecodedMode);
    QString host(QUrl::ComponentFormattingOptions = QUrl::FullyDecoded) const;
    void setPort(int port);
    int port(int defaultPort = -1) const;
    void setPath(const QString &path, QUrl::ParsingMode mode = QUrl::DecodedMode);
    QString path(QUrl::ComponentFormattingOptions options = QUrl::FullyDecoded) const;
    void setFragment(const QString &fragment, QUrl::ParsingMode mode = QUrl::TolerantMode);
    QString fragment(QUrl::ComponentFormattingOptions options = QUrl::PrettyDecoded) const;
    QUrl resolved(const QUrl &relative) const;
    bool isRelative() const;
    bool isParentOf(const QUrl &url) const;
    static QUrl fromLocalFile(const QString &localfile);
    QString toLocalFile() const;
    QString toString(QUrl::FormattingOptions options = QUrl::FormattingOptions(QUrl::PrettyDecoded)) const;
    QString toString(QUrl::ComponentFormattingOptions options) const;
    QByteArray toEncoded(QUrl::FormattingOptions options = QUrl::FullyEncoded) const;
    QByteArray toEncoded(QUrl::ComponentFormattingOptions options) const;
%If (Qt_6_7_0 -)
    static QUrl fromEncoded(QByteArrayView input, QUrl::ParsingMode mode = QUrl::TolerantMode);
%End
%If (- Qt_6_7_0)
    static QUrl fromEncoded(const QByteArray &u, QUrl::ParsingMode mode = QUrl::TolerantMode);
%End
    void detach();
    bool isDetached() const;
%If (- Qt_6_8_0)
    bool operator<(const QUrl &url) const;
%End
%If (- Qt_6_8_0)
    bool operator==(const QUrl &url) const;
%End
%If (- Qt_6_8_0)
    bool operator!=(const QUrl &url) const;
%End
    static QString fromPercentEncoding(const QByteArray &);
    static QByteArray toPercentEncoding(const QString &input, const QByteArray &exclude = QByteArray(), const QByteArray &include = QByteArray());
    bool hasQuery() const;
    bool hasFragment() const;
    QString errorString() const;
%If (Qt_6_3_0 -)
    static QString fromAce(const QByteArray &domain, QUrl::AceProcessingOptions options = {});
%End
%If (- Qt_6_3_0)
    static QString fromAce(const QByteArray &);
%End
%If (Qt_6_3_0 -)
    static QByteArray toAce(const QString &domain, QUrl::AceProcessingOptions options = {});
%End
%If (- Qt_6_3_0)
    static QByteArray toAce(const QString &);
%End
    static QStringList idnWhitelist();
    static void setIdnWhitelist(const QStringList &);
    static QUrl fromUserInput(const QString &userInput, const QString &workingDirectory = QString(), QUrl::UserInputResolutionOptions options = QUrl::DefaultResolution);
    void swap(QUrl &other /Constrained/);
    bool isLocalFile() const;
    QString toDisplayString(QUrl::FormattingOptions options = QUrl::FormattingOptions(QUrl::PrettyDecoded)) const;
    QString toDisplayString(QUrl::ComponentFormattingOptions options) const;
    void setQuery(const QString &query, QUrl::ParsingMode mode = QUrl::TolerantMode);
    void setQuery(const QUrlQuery &query);
    QString query(QUrl::ComponentFormattingOptions options = QUrl::PrettyDecoded) const;
    static QStringList toStringList(const QList<QUrl> &uris, QUrl::FormattingOptions options = QUrl::FormattingOptions(QUrl::PrettyDecoded));
    static QList<QUrl> fromStringList(const QStringList &uris, QUrl::ParsingMode mode = QUrl::TolerantMode);
    QUrl adjusted(QUrl::FormattingOptions options) const;
    QString fileName(QUrl::ComponentFormattingOptions options = QUrl::FullyDecoded) const;
    bool matches(const QUrl &url, QUrl::FormattingOptions options) const;

    enum UserInputResolutionOption /BaseType=Flag/
    {
        DefaultResolution,
        AssumeLocalFile,
    };

    typedef QFlags<QUrl::UserInputResolutionOption> UserInputResolutionOptions;
%If (Qt_6_3_0 -)

    enum AceProcessingOption /BaseType=Flag/
    {
        IgnoreIDNWhitelist,
        AceTransitionalProcessing,
    };

%End
%If (Qt_6_3_0 -)
    typedef QFlags<QUrl::AceProcessingOption> AceProcessingOptions;
%End
};

QDataStream &operator<<(QDataStream &, const QUrl &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QUrl & /Constrained/) /ReleaseGIL/;
%If (Qt_6_8_0 -)
bool operator>=(const QUrl &lhs, const QUrl &rhs);
%End
%If (Qt_6_8_0 -)
bool operator<=(const QUrl &lhs, const QUrl &rhs);
%End
%If (Qt_6_8_0 -)
bool operator>(const QUrl &lhs, const QUrl &rhs);
%End
%If (Qt_6_8_0 -)
bool operator<(const QUrl &lhs, const QUrl &rhs);
%End
%If (Qt_6_8_0 -)
bool operator!=(const QUrl &lhs, const QUrl &rhs);
%End
%If (Qt_6_8_0 -)
bool operator==(const QUrl &lhs, const QUrl &rhs);
%End
