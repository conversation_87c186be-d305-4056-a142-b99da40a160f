// qtimezone.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTimeZone
{
%TypeHeaderCode
#include <qtimezone.h>
%End

public:
    enum TimeType
    {
        StandardTime,
        DaylightTime,
        GenericTime,
    };

    enum NameType
    {
        DefaultName,
        LongName,
        ShortName,
        OffsetName,
    };

    struct OffsetData
    {
%TypeHeaderCode
#include <qtimezone.h>
%End

        QString abbreviation;
        QDateTime atUtc;
        int offsetFromUtc;
        int standardTimeOffset;
        int daylightTimeOffset;
    };

    typedef QList<QTimeZone::OffsetData> OffsetDataList;
%If (Qt_6_5_0 -)
    QTimeZone(QTimeZone::Initialization spec);
%End
%If (Qt_6_2_0 -)
    QTimeZone(const QByteArray &zoneId, int offsetSeconds, const QString &name, const QString &abbreviation, QLocale::Territory territory = QLocale::AnyTerritory, const QString &comment = QString());
%End
%If (- Qt_6_2_0)
    QTimeZone(const QByteArray &zoneId, int offsetSeconds, const QString &name, const QString &abbreviation, QLocale::Country country = QLocale::AnyCountry, const QString &comment = QString());
%End
    explicit QTimeZone(const QByteArray &ianaId);
    explicit QTimeZone(int offsetSeconds);
    QTimeZone(const QTimeZone &other);
    QTimeZone();
    ~QTimeZone();
    void swap(QTimeZone &other /Constrained/);
%If (- Qt_6_7_0)
    bool operator==(const QTimeZone &other) const;
%End
%If (- Qt_6_7_0)
    bool operator!=(const QTimeZone &other) const;
%End
    bool isValid() const;
    QByteArray id() const;
    QLocale::Country country() const;
%If (Qt_6_2_0 -)
    QLocale::Territory territory() const;
%End
    QString comment() const;
    QString displayName(const QDateTime &atDateTime, QTimeZone::NameType nameType = QTimeZone::DefaultName, const QLocale &locale = QLocale()) const;
    QString displayName(QTimeZone::TimeType timeType, QTimeZone::NameType nameType = QTimeZone::DefaultName, const QLocale &locale = QLocale()) const;
    QString abbreviation(const QDateTime &atDateTime) const;
    int offsetFromUtc(const QDateTime &atDateTime) const;
    int standardTimeOffset(const QDateTime &atDateTime) const;
    int daylightTimeOffset(const QDateTime &atDateTime) const;
    bool hasDaylightTime() const;
    bool isDaylightTime(const QDateTime &atDateTime) const;
    QTimeZone::OffsetData offsetData(const QDateTime &forDateTime) const;
    bool hasTransitions() const;
    QTimeZone::OffsetData nextTransition(const QDateTime &afterDateTime) const;
    QTimeZone::OffsetData previousTransition(const QDateTime &beforeDateTime) const;
    QTimeZone::OffsetDataList transitions(const QDateTime &fromDateTime, const QDateTime &toDateTime) const;
    static QByteArray systemTimeZoneId();
    static bool isTimeZoneIdAvailable(const QByteArray &ianaId);
%If (Qt_6_2_0 -)
    static QList<QByteArray> availableTimeZoneIds(QLocale::Territory territory /Constrained/);
%End
%If (- Qt_6_2_0)
    static QList<QByteArray> availableTimeZoneIds(QLocale::Country country /Constrained/);
%End
    static QList<QByteArray> availableTimeZoneIds(int offsetSeconds);
    static QList<QByteArray> availableTimeZoneIds();
    static QByteArray ianaIdToWindowsId(const QByteArray &ianaId);
    static QByteArray windowsIdToDefaultIanaId(const QByteArray &windowsId);
%If (Qt_6_2_0 -)
    static QByteArray windowsIdToDefaultIanaId(const QByteArray &windowsId, QLocale::Territory territory);
%End
%If (- Qt_6_2_0)
    static QByteArray windowsIdToDefaultIanaId(const QByteArray &windowsId, QLocale::Country country);
%End
    static QList<QByteArray> windowsIdToIanaIds(const QByteArray &windowsId);
%If (Qt_6_2_0 -)
    static QList<QByteArray> windowsIdToIanaIds(const QByteArray &windowsId, QLocale::Territory territory);
%End
%If (- Qt_6_2_0)
    static QList<QByteArray> windowsIdToIanaIds(const QByteArray &windowsId, QLocale::Country country);
%End
    static QTimeZone systemTimeZone();
    static QTimeZone utc();
%If (Qt_6_5_0 -)

    enum Initialization
    {
        LocalTime,
        UTC,
    };

%End
%If (Qt_6_5_0 -)
    static QTimeZone fromSecondsAheadOfUtc(int offset);
%End
%If (Qt_6_5_0 -)
    Qt::TimeSpec timeSpec() const;
%End
%If (Qt_6_5_0 -)
    int fixedSecondsAheadOfUtc() const;
%End
%If (Qt_6_5_0 -)
    bool isUtcOrFixedOffset() const;
%End
%If (Qt_6_5_0 -)
    static bool isUtcOrFixedOffset(Qt::TimeSpec spec);
%End
%If (Qt_6_5_0 -)
    QTimeZone asBackendZone() const;
%End
%If (Qt_6_6_0 -)
    static const int MinUtcOffsetSecs;
%End
%If (Qt_6_6_0 -)
    static const int MaxUtcOffsetSecs;
%End
%If (Qt_6_8_0 -)
    bool hasAlternativeName(QByteArrayView alias) const;
%End
};

QDataStream &operator<<(QDataStream &ds, const QTimeZone &tz) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &ds, QTimeZone &tz /Constrained/) /ReleaseGIL/;
%If (Qt_6_7_0 -)
bool operator==(const QTimeZone &lhs, const QTimeZone &rhs);
%End
%If (Qt_6_7_0 -)
bool operator!=(const QTimeZone &lhs, const QTimeZone &rhs);
%End
