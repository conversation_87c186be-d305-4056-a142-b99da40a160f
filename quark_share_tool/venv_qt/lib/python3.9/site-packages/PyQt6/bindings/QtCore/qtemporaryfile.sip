// qtemporaryfile.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTemporaryFile : public QFile
{
%TypeHeaderCode
#include <qtemporaryfile.h>
%End

public:
    QTemporaryFile();
    explicit QTemporaryFile(const QString &templateName);
    explicit QTemporaryFile(QObject *parent /TransferThis/);
    QTemporaryFile(const QString &templateName, QObject *parent /TransferThis/);
    virtual ~QTemporaryFile();
    bool autoRemove() const;
    void setAutoRemove(bool b);
    bool open() /ReleaseGIL/;
    virtual QString fileName() const;
    QString fileTemplate() const;
    void setFileTemplate(const QString &name);
    static QTemporaryFile *createNativeFile(const QString &fileName) /Factory,ReleaseGIL/;
    static QTemporaryFile *createNativeFile(QFile &file) /Factory,ReleaseGIL/;
    bool rename(const QString &newName);

protected:
    virtual bool open(QIODeviceBase::OpenMode flags) /ReleaseGIL/;
};
