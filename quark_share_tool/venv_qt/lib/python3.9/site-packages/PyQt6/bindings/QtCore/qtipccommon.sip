// qtipccommon.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_6_0 -)

class QNativeIpcKey
{
%TypeHeaderCode
#include <qtipccommon.h>
%End

public:
    enum class Type
    {
        SystemV,
        PosixRealtime,
        Windows,
    };

    static const QNativeIpcKey::Type DefaultTypeForOs;
    static QNativeIpcKey::Type legacyDefaultTypeForOs();
    QNativeIpcKey();
    explicit QNativeIpcKey(QNativeIpcKey::Type type);
    QNativeIpcKey(const QString &k, QNativeIpcKey::Type type = QNativeIpcKey::DefaultTypeForOs);
    QNativeIpcKey(const QNativeIpcKey &other);
    ~QNativeIpcKey();
    void swap(QNativeIpcKey &other /Constrained/);
    bool isEmpty() const;
    bool isValid() const;
    QNativeIpcKey::Type type() const;
    void setType(QNativeIpcKey::Type type);
    QString nativeKey() const;
    void setNativeKey(const QString &newKey);
    QString toString() const;
    static QNativeIpcKey fromString(const QString &string);
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};

%End
%If (Qt_6_6_0 -)
bool operator==(const QNativeIpcKey &lhs, const QNativeIpcKey &rhs);
%End
%If (Qt_6_6_0 -)
bool operator!=(const QNativeIpcKey &lhs, const QNativeIpcKey &rhs);
%End
