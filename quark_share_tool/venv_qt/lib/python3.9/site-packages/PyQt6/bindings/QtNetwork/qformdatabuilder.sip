// qformdatabuilder.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_8_0 -)

class QFormDataPartBuilder
{
%TypeHeaderCode
#include <qformdatabuilder.h>
%End

public:
    QFormDataPartBuilder();
    QFormDataPartBuilder setBody(QByteArrayView data, QAnyStringView fileName = {}, QAnyStringView mimeType = {});
    QFormDataPartBuilder setBodyDevice(QIODevice *body, QAnyStringView fileName = {}, QAnyStringView mimeType = {});
    QFormDataPartBuilder setHeaders(const QHttpHeaders &headers);
    void swap(QFormDataPartBuilder &other /Constrained/);
};

%End
%If (Qt_6_8_0 -)

class QFormDataBuilder
{
%TypeHeaderCode
#include <qformdatabuilder.h>
%End

public:
    enum class Option
    {
        Default,
        OmitRfc8187EncodedFilename,
        UseRfc7578PercentEncodedFilename,
        PreferLatin1EncodedFilename,
        StrictRfc7578,
    };

    typedef QFlags<QFormDataBuilder::Option> Options;
    QFormDataBuilder();
    ~QFormDataBuilder();
    void swap(QFormDataBuilder &other /Constrained/);
    QFormDataPartBuilder part(QAnyStringView name);
    QHttpMultiPart *buildMultiPart(QFormDataBuilder::Options options = {}) /Factory/;
%MethodCode
        sipRes = sipCpp->buildMultiPart(*a0).release();
%End

private:
%If (Qt_6_8_0 -)
    QFormDataBuilder(const QFormDataBuilder &);
%End
};

%End
