// qimage.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Windows)
typedef struct HBITMAP__ *HBITMAP;
%End
%If (Windows)
typedef struct HICON__ *HICON;
%End

class QImage : public QPaintDevice
{
%TypeHeaderCode
#include <qimage.h>
%End

%TypeCode
// The C++ closure that is passed to the QImage ctor.
struct QtGui_QImage_closure
{
    PyObject *py_cleanup;
    PyObject *py_closure;
};


// Return the optional C++ closure for a QImage ctor.
static QtGui_QImage_closure *QtGui_QImage_buffer(PyObject *py_cleanup,
        PyObject *py_closure)
{
    // The cleanup callable or nullptr if there isn't one.
    if (py_cleanup == Py_None)
        py_cleanup = nullptr;

    if (!py_cleanup)
        return nullptr;
    
    QtGui_QImage_closure *closure = new QtGui_QImage_closure;

    Py_INCREF(py_cleanup);

    if (!py_closure)
        py_closure = Py_None;

    Py_INCREF(py_closure);
    
    closure->py_cleanup = py_cleanup;
    closure->py_closure = py_closure;

    return closure;
}


// The cleanup function for a QImage ctor.
static void QtGui_QImage_cleanup(void *c)
{
    SIP_BLOCK_THREADS
    
    QtGui_QImage_closure *closure = reinterpret_cast<QtGui_QImage_closure *>(c);
    bool is_exception = false;

    PyObject *res = PyObject_CallFunctionObjArgs(closure->py_cleanup,
            closure->py_closure, SIP_NULLPTR);

    Py_DECREF(closure->py_closure);
    Py_DECREF(closure->py_cleanup);

    if (res)
        Py_DECREF(res);
    else
        is_exception = true;

    delete closure;

    if (is_exception)
        pyqt6_qtgui_err_print();
    
    SIP_UNBLOCK_THREADS
}
%End

public:
    enum InvertMode
    {
        InvertRgb,
        InvertRgba,
    };

    enum Format
    {
        Format_Invalid,
        Format_Mono,
        Format_MonoLSB,
        Format_Indexed8,
        Format_RGB32,
        Format_ARGB32,
        Format_ARGB32_Premultiplied,
        Format_RGB16,
        Format_ARGB8565_Premultiplied,
        Format_RGB666,
        Format_ARGB6666_Premultiplied,
        Format_RGB555,
        Format_ARGB8555_Premultiplied,
        Format_RGB888,
        Format_RGB444,
        Format_ARGB4444_Premultiplied,
        Format_RGBX8888,
        Format_RGBA8888,
        Format_RGBA8888_Premultiplied,
        Format_BGR30,
        Format_A2BGR30_Premultiplied,
        Format_RGB30,
        Format_A2RGB30_Premultiplied,
        Format_Alpha8,
        Format_Grayscale8,
        Format_RGBX64,
        Format_RGBA64,
        Format_RGBA64_Premultiplied,
        Format_Grayscale16,
        Format_BGR888,
%If (Qt_6_2_0 -)
        Format_RGBX16FPx4,
%End
%If (Qt_6_2_0 -)
        Format_RGBA16FPx4,
%End
%If (Qt_6_2_0 -)
        Format_RGBA16FPx4_Premultiplied,
%End
%If (Qt_6_2_0 -)
        Format_RGBX32FPx4,
%End
%If (Qt_6_2_0 -)
        Format_RGBA32FPx4,
%End
%If (Qt_6_2_0 -)
        Format_RGBA32FPx4_Premultiplied,
%End
%If (Qt_6_8_0 -)
        Format_CMYK8888,
%End
    };

    QImage();
    QImage(const QSize &size, QImage::Format format);
    QImage(int width, int height, QImage::Format format);
    QImage(const uchar *data /KeepReference/, int width, int height, QImage::Format format, SIP_PYCALLABLE cleanupFunction /AllowNone/ = 0, SIP_PYOBJECT cleanupInfo /AllowNone/ = 0) [(const uchar *data, int width, int height, QImage::Format format, QImageCleanupFunction cleanupFunction = 0, void *cleanupInfo = 0)];
%MethodCode
        QtGui_QImage_closure *closure = QtGui_QImage_buffer(a4, a5);
        
        sipCpp = new sipQImage(a0, a1, a2, a3,
                (closure ? QtGui_QImage_cleanup : nullptr), closure);
%End

    QImage(const uchar *data /KeepReference/, int width, int height, qsizetype bytesPerLine, QImage::Format format, SIP_PYCALLABLE cleanupFunction /AllowNone/ = 0, SIP_PYOBJECT cleanupInfo /AllowNone/ = 0) [(const uchar *data, int width, int height, qsizetype bytesPerLine, QImage::Format format, QImageCleanupFunction cleanupFunction = 0, void *cleanupInfo = 0)];
%MethodCode
        QtGui_QImage_closure *closure = QtGui_QImage_buffer(a5, a6);
        
        sipCpp = new sipQImage(a0, a1, a2, a3, a4,
                (closure ? QtGui_QImage_cleanup : nullptr), closure);
%End

    QImage(void *data /KeepReference/, int width, int height, QImage::Format format, SIP_PYCALLABLE cleanupFunction /AllowNone/ = 0, SIP_PYOBJECT cleanupInfo /AllowNone/ = 0) /NoTypeHint/ [(uchar *data, int width, int height, QImage::Format format, QImageCleanupFunction cleanupFunction = 0, void *cleanupInfo = 0)];
%MethodCode
        QtGui_QImage_closure *closure = QtGui_QImage_buffer(a4, a5);
        
        sipCpp = new sipQImage(reinterpret_cast<uchar *>(a0), a1, a2, a3,
                (closure ? QtGui_QImage_cleanup : nullptr), closure);
%End

    QImage(void *data /KeepReference/, int width, int height, qsizetype bytesPerLine, QImage::Format format, SIP_PYCALLABLE cleanupFunction /AllowNone/ = 0, SIP_PYOBJECT cleanupInfo /AllowNone/ = 0) /NoTypeHint/ [(uchar *data, int width, int height, qsizetype bytesPerLine, QImage::Format format, QImageCleanupFunction cleanupFunction = 0, void *cleanupInfo = 0)];
%MethodCode
        QtGui_QImage_closure *closure = QtGui_QImage_buffer(a5, a6);
        
        sipCpp = new sipQImage(reinterpret_cast<uchar *>(a0), a1, a2, a3, a4,
                (closure ? QtGui_QImage_cleanup : nullptr), closure);
%End

    explicit QImage(SIP_PYLIST xpm /TypeHint="List[str]"/) [(const char * const *xpm)];
%MethodCode
        // The Python interface is a list of ASCII strings that make up the image.
        
        const char **str = QtGui_ListToArray(a0);
        
        if (str)
        {
            sipCpp = new sipQImage(str);
            QtGui_DeleteArray(str);
        }
        else
            sipIsErr = 1;
%End

    QImage(const QString &fileName, const char *format = 0) /ReleaseGIL/;
    QImage(const QImage &);
    QImage(const QVariant &variant /GetWrapper/) /NoDerived/;
%MethodCode
        if (a0->canConvert<QImage>())
            sipCpp = new sipQImage(a0->value<QImage>());
        else
            sipError = sipBadCallableArg(0, a0Wrapper);
%End

    virtual ~QImage();
    bool isNull() const;
    bool operator==(const QImage &) const;
    bool operator!=(const QImage &) const;
    QImage copy(const QRect &rect = QRect()) const;
    QImage copy(int x, int y, int w, int h) const;
    QImage::Format format() const;
    QImage convertToFormat(QImage::Format f, Qt::ImageConversionFlags flags = Qt::AutoColor) const;
    QImage convertToFormat(QImage::Format f, const QList<unsigned int> &colorTable, Qt::ImageConversionFlags flags = Qt::AutoColor) const;
%If (Windows)
    HBITMAP toHBITMAP() const;
%End
%If (Windows)
    static QImage fromHBITMAP(HBITMAP hbitmap);
%End
%If (Windows)
    HICON toHICON(const QImage &mask = {}) const;
%End
%If (Windows)
    static QImage fromHICON(HICON hicon);
%End
    int width() const;
    int height() const;
    QSize size() const;
    QRect rect() const;
    int depth() const;
    QRgb color(int i) const;
    void setColor(int i, QRgb c);
    bool allGray() const;
    bool isGrayscale() const;
    void *bits() [uchar * ()];
    const void *constBits() const [const uchar * ()];
    void *scanLine(int) [uchar * (int)];
    const void *constScanLine(int) const [const uchar * (int)];
    qsizetype bytesPerLine() const;
    bool valid(const QPoint &pt) const;
    bool valid(int x, int y) const;
    int pixelIndex(const QPoint &pt) const;
    int pixelIndex(int x, int y) const;
    QRgb pixel(const QPoint &pt) const;
    QRgb pixel(int x, int y) const;
    void setPixel(const QPoint &pt, uint index_or_rgb);
    void setPixel(int x, int y, uint index_or_rgb);
    QList<unsigned int> colorTable() const;
    void setColorTable(const QList<unsigned int> &colors);
    void fill(uint pixel);
    void fill(Qt::GlobalColor color /Constrained/);
    void fill(const QColor &color);
    bool hasAlphaChannel() const;
    void setAlphaChannel(const QImage &alphaChannel);
    QImage createAlphaMask(Qt::ImageConversionFlags flags = Qt::AutoColor) const;
    QImage createHeuristicMask(bool clipTight = true) const;
    QImage scaled(int width, int height, Qt::AspectRatioMode aspectRatioMode = Qt::IgnoreAspectRatio, Qt::TransformationMode transformMode = Qt::FastTransformation) const;
    QImage scaled(const QSize &size, Qt::AspectRatioMode aspectRatioMode = Qt::IgnoreAspectRatio, Qt::TransformationMode transformMode = Qt::FastTransformation) const;
    QImage scaledToWidth(int width, Qt::TransformationMode mode = Qt::FastTransformation) const;
    QImage scaledToHeight(int height, Qt::TransformationMode mode = Qt::FastTransformation) const;
    QImage mirrored(bool horizontal = false, bool vertical = true) const;
    void mirror(bool horizontal = false, bool vertical = true);
    QImage rgbSwapped() const;
    void rgbSwap();
    void invertPixels(QImage::InvertMode mode = QImage::InvertRgb);
    bool load(QIODevice *device, const char *format) /ReleaseGIL/;
    bool load(const QString &fileName, const char *format = 0) /ReleaseGIL/;
    bool loadFromData(const uchar *data /Array/, int len /ArraySize/, const char *format = 0);
    bool loadFromData(const QByteArray &data, const char *format = 0);
    bool save(const QString &fileName, const char *format = 0, int quality = -1) const /ReleaseGIL/;
    bool save(QIODevice *device, const char *format = 0, int quality = -1) const /ReleaseGIL/;
    static QImage fromData(const uchar *data /Array/, int size /ArraySize/, const char *format = 0);
    static QImage fromData(const QByteArray &data, const char *format = 0);
    virtual QPaintEngine *paintEngine() const;
    int dotsPerMeterX() const;
    int dotsPerMeterY() const;
    void setDotsPerMeterX(int);
    void setDotsPerMeterY(int);
    QPoint offset() const;
    void setOffset(const QPoint &);
    QStringList textKeys() const;
    QString text(const QString &key = QString()) const;
    void setText(const QString &key, const QString &value);
    QImage createMaskFromColor(QRgb color, Qt::MaskMode mode = Qt::MaskInColor) const;
    QImage transformed(const QTransform &matrix, Qt::TransformationMode mode = Qt::FastTransformation) const;
    static QTransform trueMatrix(const QTransform &, int w, int h);
    qint64 cacheKey() const;
    int colorCount() const;
    void setColorCount(int);
    int bitPlaneCount() const;
    void swap(QImage &other /Constrained/);
    qreal devicePixelRatio() const;
    void setDevicePixelRatio(qreal scaleFactor);
    QPixelFormat pixelFormat() const;
    static QPixelFormat toPixelFormat(QImage::Format format);
    static QImage::Format toImageFormat(QPixelFormat format);
    QColor pixelColor(int x, int y) const;
    QColor pixelColor(const QPoint &pt) const;
    void setPixelColor(int x, int y, const QColor &c);
    void setPixelColor(const QPoint &pt, const QColor &c);
    bool reinterpretAsFormat(QImage::Format f);
    qsizetype sizeInBytes() const;
    QImage convertedTo(QImage::Format f, Qt::ImageConversionFlags flags = Qt::AutoColor) const;
    void convertTo(QImage::Format f, Qt::ImageConversionFlags flags = Qt::AutoColor);
    QColorSpace colorSpace() const;
    void setColorSpace(const QColorSpace &);
    QImage convertedToColorSpace(const QColorSpace &) const;
%If (Qt_6_8_0 -)
    QImage convertedToColorSpace(const QColorSpace &colorSpace, QImage::Format format, Qt::ImageConversionFlags flags = Qt::AutoColor);
%End
    void convertToColorSpace(const QColorSpace &);
%If (Qt_6_8_0 -)
    void convertToColorSpace(const QColorSpace &colorSpace, QImage::Format format, Qt::ImageConversionFlags flags = Qt::AutoColor);
%End
    void applyColorTransform(const QColorTransform &transform);
%If (Qt_6_8_0 -)
    void applyColorTransform(const QColorTransform &transform, QImage::Format format, Qt::ImageConversionFlags flags = Qt::AutoColor);
%End
%If (Qt_6_2_0 -)
    QSizeF deviceIndependentSize() const;
%End
%If (Qt_6_4_0 -)
    QImage colorTransformed(const QColorTransform &transform) const;
%End
%If (Qt_6_8_0 -)
    QImage colorTransformed(const QColorTransform &transform, QImage::Format format, Qt::ImageConversionFlags flags = Qt::AutoColor);
%End
%If (Qt_6_9_0 -)
    QImage flipped(Qt::Orientations orient = Qt::Vertical) const;
%End
%If (Qt_6_9_0 -)
    void flip(Qt::Orientations orient = Qt::Vertical);
%End
};

QDataStream &operator<<(QDataStream &, const QImage &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QImage & /Constrained/) /ReleaseGIL/;

%ModuleHeaderCode
// Helpers for QImage and QPixmap ctors.
const char **QtGui_ListToArray(PyObject *lst);
void QtGui_DeleteArray(const char **arr);

// Imports from QtCore.
typedef void (*pyqt6_qtgui_err_print_t)();
extern pyqt6_qtgui_err_print_t pyqt6_qtgui_err_print;
%End

%ModuleCode
// Convert a list of strings to an array of ASCII strings on the heap.  Used by
// QImage and QPixmap.
const char **QtGui_ListToArray(PyObject *lst)
{
    Py_ssize_t nstr = PyList_Size(lst);
    const char **arr = new const char *[nstr + 1];

    for (Py_ssize_t i = 0; i < nstr; ++i)
    {
        PyObject *ascii_obj = PyList_GetItem(lst, i);
        const char *ascii = sipString_AsASCIIString(&ascii_obj);

        if (!ascii)
        {
            while (i-- > 0)
                delete[] arr[i];

            delete[] arr;

            return 0;
        }

        // Copy the string.
        arr[i] = qstrdup(ascii);

        Py_DECREF(ascii_obj);
    }

    // The sentinal.
    arr[nstr] = 0;

    return arr;
}


// Return a string array created by QtGui_ListToArray() to the heap.
void QtGui_DeleteArray(const char **arr)
{
    for (Py_ssize_t i = 0; arr[i]; ++i)
        delete[] arr[i];

    delete[] arr;
}

// Imports from QtCore.
pyqt6_qtgui_err_print_t pyqt6_qtgui_err_print;
%End

%PostInitialisationCode
// Imports from QtCore.
pyqt6_qtgui_err_print = (pyqt6_qtgui_err_print_t)sipImportSymbol("pyqt6_err_print");
Q_ASSERT(pyqt6_qtgui_err_print);
%End
