// qactiongroup.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QActionGroup : public QObject
{
%TypeHeaderCode
#include <qactiongroup.h>
%End

public:
    explicit QActionGroup(QObject *parent /TransferThis/);
    virtual ~QActionGroup();
    QAction *addAction(QAction *a /Transfer/);
    QAction *addAction(const QString &text) /Transfer/;
    QAction *addAction(const QIcon &icon, const QString &text) /Transfer/;
    void removeAction(QAction *a /TransferBack/);
    QList<QAction *> actions() const;
    QAction *checkedAction() const;
    bool isExclusive() const;
    bool isEnabled() const;
    bool isVisible() const;

    enum class ExclusionPolicy
    {
        None,
        Exclusive,
        ExclusiveOptional,
    };

    QActionGroup::ExclusionPolicy exclusionPolicy() const;

public slots:
    void setEnabled(bool);
    void setDisabled(bool b);
    void setVisible(bool);
    void setExclusive(bool);
    void setExclusionPolicy(QActionGroup::ExclusionPolicy policy);

signals:
    void triggered(QAction *);
    void hovered(QAction *);
};
