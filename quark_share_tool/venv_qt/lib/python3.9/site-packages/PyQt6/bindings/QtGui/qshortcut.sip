// qshortcut.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QShortcut : public QObject
{
%TypeHeaderCode
#include <qshortcut.h>
%End

public:
    QShortcut(QKeySequence::StandardKey key, QObject *parent /TransferThis/, SIP_PYOBJECT member /TypeHint="PYQT_SLOT"/ = 0, SIP_PYOBJECT ambiguousMember /TypeHint="PYQT_SLOT"/ = 0, Qt::ShortcutContext context = Qt::WindowShortcut) [(QKeySequence::StandardKey key, QObject *parent, const char *member = 0, const char *ambiguousMember = 0, Qt::ShortcutContext context = Qt::WindowShortcut)];
%MethodCode
        // Construct the shortcut without any connections.
        Py_BEGIN_ALLOW_THREADS
        sipCpp = new sipQShortcut(a0, a1, 0, 0, a4);
        Py_END_ALLOW_THREADS
        
        if (a2)
        {
            QObject *rx2;
            QByteArray member2;
        
            if ((sipError = pyqt6_qtgui_get_connection_parts(a2, sipCpp, "()", false, &rx2, member2)) == sipErrorNone)
            {
                Py_BEGIN_ALLOW_THREADS
                QObject::connect(sipCpp, SIGNAL(activated()), rx2,
                        member2.constData());
                Py_END_ALLOW_THREADS
            }
            else
            {
                delete sipCpp;
                
                if (sipError == sipErrorContinue)
                    sipError = sipBadCallableArg(2, a2);
            }
        }
        
        if (a3)
        {
            QObject *rx3;
            QByteArray member3;
        
            if ((sipError = pyqt6_qtgui_get_connection_parts(a3, sipCpp, "()", false, &rx3, member3)) == sipErrorNone)
            {
                Py_BEGIN_ALLOW_THREADS
                QObject::connect(sipCpp, SIGNAL(activatedAmbiguously()), rx3,
                        member3.constData());
                Py_END_ALLOW_THREADS
            }
            else
            {
                delete sipCpp;
                
                if (sipError == sipErrorContinue)
                    sipError = sipBadCallableArg(3, a3);
            }
        }
%End

    QShortcut(const QKeySequence &key, QObject *parent /TransferThis/, SIP_PYOBJECT member /TypeHint="PYQT_SLOT"/ = 0, SIP_PYOBJECT ambiguousMember /TypeHint="PYQT_SLOT"/ = 0, Qt::ShortcutContext context = Qt::WindowShortcut) [(const QKeySequence &key, QObject *parent, const char *member = 0, const char *ambiguousMember = 0, Qt::ShortcutContext context = Qt::WindowShortcut)];
%MethodCode
        // Construct the shortcut without any connections.
        Py_BEGIN_ALLOW_THREADS
        sipCpp = new sipQShortcut(*a0, a1, 0, 0, a4);
        Py_END_ALLOW_THREADS
        
        if (a2)
        {
            QObject *rx2;
            QByteArray member2;
        
            if ((sipError = pyqt6_qtgui_get_connection_parts(a2, sipCpp, "()", false, &rx2, member2)) == sipErrorNone)
            {
                Py_BEGIN_ALLOW_THREADS
                QObject::connect(sipCpp, SIGNAL(activated()), rx2,
                        member2.constData());
                Py_END_ALLOW_THREADS
            }
            else
            {
                delete sipCpp;
                
                if (sipError == sipErrorContinue)
                    sipError = sipBadCallableArg(2, a2);
            }
        }
        
        if (a3)
        {
            QObject *rx3;
            QByteArray member3;
        
            if ((sipError = pyqt6_qtgui_get_connection_parts(a3, sipCpp, "()", false, &rx3, member3)) == sipErrorNone)
            {
                Py_BEGIN_ALLOW_THREADS
                QObject::connect(sipCpp, SIGNAL(activatedAmbiguously()), rx3,
                        member3.constData());
                Py_END_ALLOW_THREADS
            }
            else
            {
                delete sipCpp;
                
                if (sipError == sipErrorContinue)
                    sipError = sipBadCallableArg(3, a3);
            }
        }
%End

    explicit QShortcut(QObject *parent /TransferThis/);
    virtual ~QShortcut();
    void setKey(const QKeySequence &key);
    QKeySequence key() const;
    void setEnabled(bool enable);
    bool isEnabled() const;
    void setContext(Qt::ShortcutContext context);
    Qt::ShortcutContext context() const;
    void setWhatsThis(const QString &text);
    QString whatsThis() const;
    void setAutoRepeat(bool on);
    bool autoRepeat() const;

signals:
    void activated();
    void activatedAmbiguously();

public:
    void setKeys(const QList<QKeySequence> &keys);
    void setKeys(QKeySequence::StandardKey key);
    QList<QKeySequence> keys() const;

protected:
    virtual bool event(QEvent *e);
};

%ModuleHeaderCode
// Imports from QtCore.
typedef sipErrorState (*pyqt6_qtgui_get_connection_parts_t)(PyObject *, QObject *, const char *, bool, QObject **, QByteArray &);
extern pyqt6_qtgui_get_connection_parts_t pyqt6_qtgui_get_connection_parts;
%End

%ModuleCode
// Imports from QtCore.
pyqt6_qtgui_get_connection_parts_t pyqt6_qtgui_get_connection_parts;
%End

%PostInitialisationCode
// Imports from QtCore.
pyqt6_qtgui_get_connection_parts = (pyqt6_qtgui_get_connection_parts_t)sipImportSymbol("pyqt6_get_connection_parts");
Q_ASSERT(pyqt6_qtgui_get_connection_parts);
%End
