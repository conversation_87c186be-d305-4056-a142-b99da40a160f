// qfont.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QFont
{
%TypeHeaderCode
#include <qfont.h>
%End

public:
    enum StyleHint
    {
        Helvetica,
        SansSerif,
        Times,
        Serif,
        Courier,
        TypeWriter,
        OldEnglish,
        Decorative,
        System,
        AnyStyle,
        Cursive,
        Monospace,
        Fantasy,
    };

    enum StyleStrategy /BaseType=Flag/
    {
        PreferDefault,
        PreferBitmap,
        PreferDevice,
        PreferOutline,
        ForceOutline,
        PreferMatch,
        PreferQuality,
        PreferAntialias,
        NoAntialias,
        NoSubpixelAntialias,
        NoFontMerging,
        PreferNoShaping,
%If (Qt_6_8_0 -)
        ContextFontMerging,
%End
%If (Qt_6_8_0 -)
        PreferTypoLineMetrics,
%End
    };

    enum Weight /BaseType=IntEnum/
    {
        Thin,
        ExtraLight,
        Light,
        Normal,
        Medium,
        DemiBold,
        Bold,
        ExtraBold,
        Black,
    };

    enum Style
    {
        StyleNormal,
        StyleItalic,
        StyleOblique,
    };

    enum Stretch /BaseType=IntEnum/
    {
        AnyStretch,
        UltraCondensed,
        ExtraCondensed,
        Condensed,
        SemiCondensed,
        Unstretched,
        SemiExpanded,
        Expanded,
        ExtraExpanded,
        UltraExpanded,
    };

    QFont();
%If (Qt_6_1_0 -)
    QFont(const QStringList &families, int pointSize = -1, int weight = -1, bool italic = false);
%End
    QFont(const QString &family, int pointSize = -1, int weight = -1, bool italic = false);
    QFont(const QFont &);
    QFont(const QVariant &variant /GetWrapper/) /NoDerived/;
%MethodCode
        if (a0->canConvert<QFont>())
            sipCpp = new QFont(a0->value<QFont>());
        else
            sipError = sipBadCallableArg(0, a0Wrapper);
%End

    ~QFont();
    QString family() const;
    void setFamily(const QString &);
    int pointSize() const;
    void setPointSize(int);
    qreal pointSizeF() const;
    void setPointSizeF(qreal);
    int pixelSize() const;
    void setPixelSize(int);
    int weight() const [QFont::Weight ()];
    void setWeight(int weight);
%MethodCode
        sipCpp->setWeight(QFont::Weight(a0));
%End

    void setStyle(QFont::Style style);
    QFont::Style style() const;
    bool underline() const;
    void setUnderline(bool);
    bool overline() const;
    void setOverline(bool);
    bool strikeOut() const;
    void setStrikeOut(bool);
    bool fixedPitch() const;
    void setFixedPitch(bool);
    bool kerning() const;
    void setKerning(bool);
    QFont::StyleHint styleHint() const;
    QFont::StyleStrategy styleStrategy() const;
    void setStyleHint(QFont::StyleHint hint, QFont::StyleStrategy strategy = QFont::PreferDefault);
    void setStyleStrategy(QFont::StyleStrategy s);
    int stretch() const;
    void setStretch(int);
    bool exactMatch() const;
    bool operator==(const QFont &) const;
    bool operator!=(const QFont &) const;
    bool operator<(const QFont &) const;
    bool isCopyOf(const QFont &) const;
    QString key() const;
    QString toString() const;
    bool fromString(const QString &);
    static QString substitute(const QString &);
    static QStringList substitutes(const QString &);
    static QStringList substitutions();
    static void insertSubstitution(const QString &, const QString &);
    static void insertSubstitutions(const QString &, const QStringList &);
    static void removeSubstitutions(const QString &);
    static void initialize();
    static void cleanup();
    static void cacheStatistics();
    QString defaultFamily() const;
    QFont resolve(const QFont &) const;
    bool bold() const;
    void setBold(bool enable);
    bool italic() const;
    void setItalic(bool b);

    enum Capitalization
    {
        MixedCase,
        AllUppercase,
        AllLowercase,
        SmallCaps,
        Capitalize,
    };

    enum SpacingType
    {
        PercentageSpacing,
        AbsoluteSpacing,
    };

    qreal letterSpacing() const;
    QFont::SpacingType letterSpacingType() const;
    void setLetterSpacing(QFont::SpacingType type, qreal spacing);
    qreal wordSpacing() const;
    void setWordSpacing(qreal spacing);
    void setCapitalization(QFont::Capitalization);
    QFont::Capitalization capitalization() const;

    enum HintingPreference
    {
        PreferDefaultHinting,
        PreferNoHinting,
        PreferVerticalHinting,
        PreferFullHinting,
    };

    QString styleName() const;
    void setStyleName(const QString &styleName);
    void setHintingPreference(QFont::HintingPreference hintingPreference);
    QFont::HintingPreference hintingPreference() const;
    void swap(QFont &other /Constrained/);
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

    QStringList families() const;
    void setFamilies(const QStringList &);
%If (Qt_6_7_0 -)
    void setFeature(QFont::Tag tag, quint32 value);
%End
%If (Qt_6_6_0 - Qt_6_7_0)
    void setFeature(quint32 tag, quint32 value);
%End
%If (Qt_6_6_0 - Qt_6_7_0)
    void setFeature(const char *feature /Encoding="None"/, quint32 value);
%End
%If (Qt_6_7_0 -)
    void unsetFeature(QFont::Tag tag);
%End
%If (Qt_6_6_0 - Qt_6_7_0)
    void unsetFeature(const char *feature /Encoding="None"/);
%End
%If (Qt_6_6_0 - Qt_6_7_0)
    void unsetFeature(quint32 tag);
%End
%If (Qt_6_7_0 -)
    quint32 featureValue(QFont::Tag tag) const;
%End
%If (Qt_6_6_0 - Qt_6_7_0)
    quint32 featureValue(quint32 tag) const;
%End
%If (Qt_6_7_0 -)
    bool isFeatureSet(QFont::Tag tag) const;
%End
%If (Qt_6_6_0 - Qt_6_7_0)
    bool isFeatureSet(quint32 tag) const;
%End
%If (Qt_6_7_0 -)
    QList<QFont::Tag> featureTags() const;
%End
%If (Qt_6_6_0 - Qt_6_7_0)
    QList<unsigned int> featureTags() const;
%End
%If (Qt_6_6_0 -)
    void clearFeatures();
%End
%If (Qt_6_6_0 - Qt_6_7_0)
    static QByteArray tagToString(quint32 tag);
%End
%If (Qt_6_6_0 - Qt_6_7_0)
    static quint32 stringToTag(const char *tagString /Encoding="None"/);
%End
%If (Qt_6_7_0 -)

    struct Tag
    {
%TypeHeaderCode
#include <qfont.h>
%End

        Tag();
        Tag(QAnyStringView view);
%MethodCode
            // This is the easiest way to implement this ctor.
            std::optional<::QFont::Tag> opt_tag = ::QFont::Tag::fromString(*a0);
            
            if (opt_tag.has_value())
                sipCpp = new ::QFont::Tag(opt_tag.value());
            else
                sipCpp = new ::QFont::Tag;
%End

        bool isValid() const;
        quint32 value() const;
        QByteArray toString() const;
        static std::optional<QFont::Tag> fromValue(quint32 value);
        static std::optional<QFont::Tag> fromString(QAnyStringView view);
        Py_hash_t __hash__() const;
%MethodCode
            sipRes = qHash(*sipCpp);
%End
    };

%End
%If (Qt_6_7_0 -)
    void setVariableAxis(QFont::Tag tag, float value);
%End
%If (Qt_6_7_0 -)
    void unsetVariableAxis(QFont::Tag tag);
%End
%If (Qt_6_7_0 -)
    bool isVariableAxisSet(QFont::Tag tag) const;
%End
%If (Qt_6_7_0 -)
    float variableAxisValue(QFont::Tag tag) const;
%End
%If (Qt_6_7_0 -)
    void clearVariableAxes();
%End
%If (Qt_6_7_0 -)
    QList<QFont::Tag> variableAxisTags() const;
%End
};

QDataStream &operator<<(QDataStream &, const QFont &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QFont & /Constrained/) /ReleaseGIL/;
%If (Qt_6_7_0 -)
QDataStream &operator<<(QDataStream &, QFont::Tag) /ReleaseGIL/;
%End
%If (Qt_6_7_0 -)
QDataStream &operator>>(QDataStream &, QFont::Tag & /Constrained/) /ReleaseGIL/;
%End
%If (Qt_6_7_0 -)
bool operator>=(const QFont::Tag &lhs, const QFont::Tag &rhs);
%End
%If (Qt_6_7_0 -)
bool operator<=(const QFont::Tag &lhs, const QFont::Tag &rhs);
%End
%If (Qt_6_7_0 -)
bool operator>(const QFont::Tag &lhs, const QFont::Tag &rhs);
%End
%If (Qt_6_7_0 -)
bool operator<(const QFont::Tag &lhs, const QFont::Tag &rhs);
%End
%If (Qt_6_7_0 -)
bool operator!=(const QFont::Tag &lhs, const QFont::Tag &rhs);
%End
%If (Qt_6_7_0 -)
bool operator==(const QFont::Tag &lhs, const QFont::Tag &rhs);
%End
