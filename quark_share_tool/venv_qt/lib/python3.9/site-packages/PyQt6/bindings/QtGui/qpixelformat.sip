// qpixelformat.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qpixelformat.h>
%End

class QPixelFormat
{
%TypeHeaderCode
#include <qpixelformat.h>
%End

public:
    enum ColorModel
    {
        RGB,
        BGR,
        Indexed,
        Grayscale,
        CMYK,
        HSL,
        HSV,
        YUV,
        Alpha,
    };

    enum AlphaUsage
    {
        UsesAlpha,
        IgnoresAlpha,
    };

    enum AlphaPosition
    {
        AtBeginning,
        AtEnd,
    };

    enum AlphaPremultiplied
    {
        NotPremultiplied,
        Premultiplied,
    };

    enum TypeInterpretation
    {
        UnsignedInteger,
        UnsignedShort,
        UnsignedByte,
        FloatingPoint,
    };

    enum YUVLayout
    {
        YUV444,
        YUV422,
        YUV411,
        YUV420P,
        YUV420SP,
        YV12,
        UYVY,
        YUYV,
        NV12,
        NV21,
        IMC1,
        IMC2,
        IMC3,
        IMC4,
        Y8,
        Y16,
    };

    enum ByteOrder
    {
        LittleEndian,
        BigEndian,
        CurrentSystemEndian,
    };

    QPixelFormat();
    QPixelFormat(QPixelFormat::ColorModel mdl, uchar firstSize /PyInt/, uchar secondSize /PyInt/, uchar thirdSize /PyInt/, uchar fourthSize /PyInt/, uchar fifthSize /PyInt/, uchar alfa /PyInt/, QPixelFormat::AlphaUsage usage, QPixelFormat::AlphaPosition position, QPixelFormat::AlphaPremultiplied premult, QPixelFormat::TypeInterpretation typeInterp, QPixelFormat::ByteOrder byteOrder = QPixelFormat::CurrentSystemEndian, uchar subEnum /PyInt/ = 0);
    QPixelFormat::ColorModel colorModel() const;
    uchar channelCount() const /PyInt/;
    uchar redSize() const /PyInt/;
    uchar greenSize() const /PyInt/;
    uchar blueSize() const /PyInt/;
    uchar cyanSize() const /PyInt/;
    uchar magentaSize() const /PyInt/;
    uchar yellowSize() const /PyInt/;
    uchar blackSize() const /PyInt/;
    uchar hueSize() const /PyInt/;
    uchar saturationSize() const /PyInt/;
    uchar lightnessSize() const /PyInt/;
    uchar brightnessSize() const /PyInt/;
    uchar alphaSize() const /PyInt/;
    uchar bitsPerPixel() const /PyInt/;
    QPixelFormat::AlphaUsage alphaUsage() const;
    QPixelFormat::AlphaPosition alphaPosition() const;
    QPixelFormat::AlphaPremultiplied premultiplied() const;
    QPixelFormat::TypeInterpretation typeInterpretation() const;
    QPixelFormat::ByteOrder byteOrder() const;
    QPixelFormat::YUVLayout yuvLayout() const;
    uchar subEnum() const /PyInt/;
};

bool operator==(QPixelFormat fmt1, QPixelFormat fmt2);
bool operator!=(QPixelFormat fmt1, QPixelFormat fmt2);
QPixelFormat qPixelFormatRgba(uchar red /PyInt/, uchar green /PyInt/, uchar blue /PyInt/, uchar alfa /PyInt/, QPixelFormat::AlphaUsage usage, QPixelFormat::AlphaPosition position, QPixelFormat::AlphaPremultiplied premultiplied = QPixelFormat::NotPremultiplied, QPixelFormat::TypeInterpretation typeInterpretation = QPixelFormat::UnsignedInteger);
QPixelFormat qPixelFormatGrayscale(uchar channelSize /PyInt/, QPixelFormat::TypeInterpretation typeInterpretation = QPixelFormat::UnsignedInteger);
QPixelFormat qPixelFormatCmyk(uchar channelSize /PyInt/, uchar alphaSize /PyInt/ = 0, QPixelFormat::AlphaUsage alphaUsage = QPixelFormat::IgnoresAlpha, QPixelFormat::AlphaPosition alphaPosition = QPixelFormat::AtBeginning, QPixelFormat::TypeInterpretation typeInterpretation = QPixelFormat::UnsignedInteger);
QPixelFormat qPixelFormatHsl(uchar channelSize /PyInt/, uchar alphaSize /PyInt/ = 0, QPixelFormat::AlphaUsage alphaUsage = QPixelFormat::IgnoresAlpha, QPixelFormat::AlphaPosition alphaPosition = QPixelFormat::AtBeginning, QPixelFormat::TypeInterpretation typeInterpretation = QPixelFormat::FloatingPoint);
QPixelFormat qPixelFormatHsv(uchar channelSize /PyInt/, uchar alphaSize /PyInt/ = 0, QPixelFormat::AlphaUsage alphaUsage = QPixelFormat::IgnoresAlpha, QPixelFormat::AlphaPosition alphaPosition = QPixelFormat::AtBeginning, QPixelFormat::TypeInterpretation typeInterpretation = QPixelFormat::FloatingPoint);
QPixelFormat qPixelFormatYuv(QPixelFormat::YUVLayout layout, uchar alphaSize /PyInt/ = 0, QPixelFormat::AlphaUsage alphaUsage = QPixelFormat::IgnoresAlpha, QPixelFormat::AlphaPosition alphaPosition = QPixelFormat::AtBeginning, QPixelFormat::AlphaPremultiplied premultiplied = QPixelFormat::NotPremultiplied, QPixelFormat::TypeInterpretation typeInterpretation = QPixelFormat::UnsignedByte, QPixelFormat::ByteOrder byteOrder = QPixelFormat::LittleEndian);
QPixelFormat qPixelFormatAlpha(uchar channelSize /PyInt/, QPixelFormat::TypeInterpretation typeInterpretation = QPixelFormat::UnsignedInteger);
