// qcursor.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCursor /TypeHintIn="Union[QCursor, Qt.CursorShape]"/
{
%TypeHeaderCode
#include <qcursor.h>
%End

%ConvertToTypeCode
// SIP doesn't support automatic type convertors so we explicitly allow a
// Qt::CursorShape to be used whenever a QCursor is expected.

bool is_cursor_shape = true;
int cursor_shape = sipConvertToEnum(sipPy, sipType_Qt_CursorShape);

if (PyErr_Occurred())
{
    PyErr_Clear();
    is_cursor_shape = false;
}

if (sipIsErr == NULL)
    return (is_cursor_shape ||
            sipCanConvertToType(sipPy, sipType_QCursor, SIP_NO_CONVERTORS));

if (is_cursor_shape)
{
    *sipCppPtr = new QCursor(static_cast<Qt::CursorShape>(cursor_shape));

    return sipGetState(sipTransferObj);
}

*sipCppPtr = reinterpret_cast<QCursor *>(sipConvertToType(sipPy, sipType_QCursor, sipTransferObj, SIP_NO_CONVERTORS, 0, sipIsErr));

return 0;
%End

public:
    QCursor();
    QCursor(const QBitmap &bitmap, const QBitmap &mask, int hotX = -1, int hotY = -1);
    QCursor(const QPixmap &pixmap, int hotX = -1, int hotY = -1);
    QCursor(const QCursor &cursor);
    QCursor(const QVariant &variant /GetWrapper/) /NoDerived/;
%MethodCode
        if (a0->canConvert<QCursor>())
            sipCpp = new QCursor(a0->value<QCursor>());
        else
            sipError = sipBadCallableArg(0, a0Wrapper);
%End

    ~QCursor();
    Qt::CursorShape shape() const;
    void setShape(Qt::CursorShape newShape);
    QBitmap bitmap() const;
    QBitmap mask() const;
    QPixmap pixmap() const;
    QPoint hotSpot() const;
    static QPoint pos();
    static void setPos(int x, int y);
    static void setPos(const QPoint &p);
    static QPoint pos(const QScreen *screen);
    static void setPos(QScreen *screen, int x, int y);
    static void setPos(QScreen *screen, const QPoint &p);
    void swap(QCursor &other);
};

QDataStream &operator<<(QDataStream &outS, const QCursor &cursor) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &inS, QCursor &cursor /Constrained/) /ReleaseGIL/;
bool operator==(const QCursor &lhs, const QCursor &rhs);
bool operator!=(const QCursor &lhs, const QCursor &rhs);
