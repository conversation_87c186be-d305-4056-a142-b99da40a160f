// qfilesystemmodel.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QFileSystemModel : public QAbstractItemModel
{
%TypeHeaderCode
#include <qfilesystemmodel.h>
%End

public:
    enum Roles /BaseType=IntEnum/
    {
        FileIconRole,
        FilePathRole,
        FileNameRole,
        FilePermissions,
%If (Qt_6_8_0 -)
        FileInfoRole,
%End
    };

    explicit QFileSystemModel(QObject *parent /TransferThis/ = 0);
    virtual ~QFileSystemModel();
    virtual QModelIndex index(int row, int column, const QModelIndex &parent = QModelIndex()) const;
    QModelIndex index(const QString &path, int column = 0) const;
    virtual QModelIndex parent(const QModelIndex &child) const;
    virtual bool hasChildren(const QModelIndex &parent = QModelIndex()) const;
    virtual bool canFetchMore(const QModelIndex &parent) const;
    virtual void fetchMore(const QModelIndex &parent);
    virtual int rowCount(const QModelIndex &parent = QModelIndex()) const;
    virtual int columnCount(const QModelIndex &parent = QModelIndex()) const;
    QVariant myComputer(int role = Qt::DisplayRole) const;
    virtual QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const;
    virtual bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole);
    virtual QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const;
    virtual Qt::ItemFlags flags(const QModelIndex &index) const;
    virtual void sort(int column, Qt::SortOrder order = Qt::AscendingOrder);
    virtual QStringList mimeTypes() const;
    virtual QMimeData *mimeData(const QModelIndexList &indexes) const;
    virtual bool dropMimeData(const QMimeData *data, Qt::DropAction action, int row, int column, const QModelIndex &parent);
    virtual Qt::DropActions supportedDropActions() const;
    QModelIndex setRootPath(const QString &path);
    QString rootPath() const;
    QDir rootDirectory() const;
    void setIconProvider(QAbstractFileIconProvider *provider /KeepReference/);
    QAbstractFileIconProvider *iconProvider() const;
    void setFilter(QDir::Filters filters);
    QDir::Filters filter() const;
    void setResolveSymlinks(bool enable);
    bool resolveSymlinks() const;
    void setReadOnly(bool enable);
    bool isReadOnly() const;
    void setNameFilterDisables(bool enable);
    bool nameFilterDisables() const;
    void setNameFilters(const QStringList &filters);
    QStringList nameFilters() const;
    QString filePath(const QModelIndex &index) const;
    bool isDir(const QModelIndex &index) const;
    qint64 size(const QModelIndex &index) const;
    QString type(const QModelIndex &index) const;
    QDateTime lastModified(const QModelIndex &index) const;
%If (Qt_6_6_0 -)
    QDateTime lastModified(const QModelIndex &index, const QTimeZone &tz) const;
%End
    QModelIndex mkdir(const QModelIndex &parent, const QString &name);
    QFileDevice::Permissions permissions(const QModelIndex &index) const;
    bool rmdir(const QModelIndex &index);
    QString fileName(const QModelIndex &aindex) const;
    QIcon fileIcon(const QModelIndex &aindex) const;
    QFileInfo fileInfo(const QModelIndex &aindex) const;
    bool remove(const QModelIndex &index);

signals:
    void fileRenamed(const QString &path, const QString &oldName, const QString &newName);
    void rootPathChanged(const QString &newPath);
    void directoryLoaded(const QString &path);

protected:
    virtual bool event(QEvent *event);
    virtual void timerEvent(QTimerEvent *event);

public:
    virtual QModelIndex sibling(int row, int column, const QModelIndex &idx) const;

    enum Option /BaseType=Flag/
    {
        DontWatchForChanges,
        DontResolveSymlinks,
        DontUseCustomDirectoryIcons,
    };

    typedef QFlags<QFileSystemModel::Option> Options;
    void setOption(QFileSystemModel::Option option, bool on = true);
    bool testOption(QFileSystemModel::Option option) const;
    void setOptions(QFileSystemModel::Options options);
    QFileSystemModel::Options options() const;
    virtual QHash<int, QByteArray> roleNames() const;
};
