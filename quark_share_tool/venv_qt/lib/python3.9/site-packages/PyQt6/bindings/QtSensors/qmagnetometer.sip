// qmagnetometer.sip generated by MetaSIP
//
// This file is part of the QtSensors Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QMagnetometerReading : public QSensorReading /NoDefaultCtors/
{
%TypeHeaderCode
#include <qmagnetometer.h>
%End

public:
    qreal x() const;
    void setX(qreal x);
    qreal y() const;
    void setY(qreal y);
    qreal z() const;
    void setZ(qreal z);
    qreal calibrationLevel() const;
    void setCalibrationLevel(qreal calibrationLevel);
};

%End
%If (Qt_6_2_0 -)

class QMagnetometerFilter : public QSensorFilter
{
%TypeHeaderCode
#include <qmagnetometer.h>
%End

public:
    virtual bool filter(QMagnetometerReading *reading) = 0;
};

%End
%If (Qt_6_2_0 -)

class QMagnetometer : public QSensor
{
%TypeHeaderCode
#include <qmagnetometer.h>
%End

public:
    explicit QMagnetometer(QObject *parent /TransferThis/ = 0);
    virtual ~QMagnetometer();
    QMagnetometerReading *reading() const;
    bool returnGeoValues() const;
    void setReturnGeoValues(bool returnGeoValues);

signals:
    void returnGeoValuesChanged(bool returnGeoValues);
};

%End
