// qsignaltransition.sip generated by MetaSIP
//
// This file is part of the QtStateMachine Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSignalTransition : public QAbstractTransition
{
%TypeHeaderCode
#include <qsignaltransition.h>
%End

public:
    QSignalTransition(QState *sourceState /TransferThis/ = 0);
    QSignalTransition(SIP_PYOBJECT signal /TypeHint="pyqtBoundSignal"/, QState *sourceState /TransferThis/ = 0) /NoDerived/;
%MethodCode
        QObject *sender;
        QByteArray signal_signature;
        
        if ((sipError = pyqt6_qtstatemachine_get_pyqtsignal_parts(a0, &sender, signal_signature)) == sipErrorNone)
        {
            sipCpp = new sipQSignalTransition(a1);
            sipCpp->setSenderObject(sender);
            sipCpp->setSignal(signal_signature);
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(0, a0);
        }
%End

    virtual ~QSignalTransition();
    const QObject *senderObject() const;
    void setSenderObject(const QObject *sender);
    QByteArray signal() const;
    void setSignal(const QByteArray &signal);

protected:
    virtual bool eventTest(QEvent *event);
    virtual void onTransition(QEvent *event);
    virtual bool event(QEvent *e);

signals:
    void senderObjectChanged();
    void signalChanged();
};
