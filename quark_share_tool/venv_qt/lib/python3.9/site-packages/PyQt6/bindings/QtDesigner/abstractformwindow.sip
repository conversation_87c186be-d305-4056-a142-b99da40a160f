// abstractformwindow.sip generated by MetaSIP
//
// This file is part of the QtDesigner Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDesignerFormWindowInterface : public QWidget /Abstract/
{
%TypeHeaderCode
#include <abstractformwindow.h>
%End

public:
    enum FeatureFlag /BaseType=Flag/
    {
        EditFeature,
        GridFeature,
        TabOrderFeature,
        DefaultFeature,
    };

    typedef QFlags<QDesignerFormWindowInterface::FeatureFlag> Feature;
    QDesignerFormWindowInterface(QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = {});
    virtual ~QDesignerFormWindowInterface();
    virtual QString fileName() const = 0;
    virtual QDir absoluteDir() const = 0;
    virtual QString contents() const = 0;
    virtual bool setContents(QIODevice *dev, QString *errorMessage = 0) = 0;
    virtual QDesignerFormWindowInterface::Feature features() const = 0;
    virtual bool hasFeature(QDesignerFormWindowInterface::Feature f) const = 0;
    virtual QString author() const = 0;
    virtual void setAuthor(const QString &author) = 0;
    virtual QString comment() const = 0;
    virtual void setComment(const QString &comment) = 0;
    virtual void layoutDefault(int *margin, int *spacing) = 0;
    virtual void setLayoutDefault(int margin, int spacing) = 0;
    virtual void layoutFunction(QString *margin /Out/, QString *spacing /Out/) = 0;
    virtual void setLayoutFunction(const QString &margin, const QString &spacing) = 0;
    virtual QString pixmapFunction() const = 0;
    virtual void setPixmapFunction(const QString &pixmapFunction) = 0;
    virtual QString exportMacro() const = 0;
    virtual void setExportMacro(const QString &exportMacro) = 0;
    virtual QStringList includeHints() const = 0;
    virtual void setIncludeHints(const QStringList &includeHints) = 0;
    virtual QDesignerFormEditorInterface *core() const;
    virtual QDesignerFormWindowCursorInterface *cursor() const = 0;
    virtual QPoint grid() const = 0;
    virtual QWidget *mainContainer() const = 0;
    virtual void setMainContainer(QWidget *mainContainer /KeepReference/) = 0;
    virtual bool isManaged(QWidget *widget) const = 0;
    virtual bool isDirty() const = 0;
    static QDesignerFormWindowInterface *findFormWindow(QWidget *w);
    static QDesignerFormWindowInterface *findFormWindow(QObject *obj);
    virtual void emitSelectionChanged() = 0;
    virtual QStringList resourceFiles() const = 0;
    virtual void addResourceFile(const QString &path) = 0;
    virtual void removeResourceFile(const QString &path) = 0;

public slots:
    virtual void manageWidget(QWidget *widget) = 0;
    virtual void unmanageWidget(QWidget *widget) = 0;
    virtual void setFeatures(QDesignerFormWindowInterface::Feature f) = 0;
    virtual void setDirty(bool dirty) = 0;
    virtual void clearSelection(bool update = true) = 0;
    virtual void selectWidget(QWidget *widget, bool select = true) = 0;
    virtual void setGrid(const QPoint &grid) = 0;
    virtual void setFileName(const QString &fileName) = 0;
    virtual bool setContents(const QString &contents) = 0;

signals:
    void mainContainerChanged(QWidget *mainContainer);
    void fileNameChanged(const QString &fileName);
    void featureChanged(QDesignerFormWindowInterface::Feature f /ScopesStripped=1/);
    void selectionChanged();
    void geometryChanged();
    void resourceFilesChanged();
    void widgetManaged(QWidget *widget);
    void widgetUnmanaged(QWidget *widget);
    void aboutToUnmanageWidget(QWidget *widget);
    void activated(QWidget *widget);
    void changed();
    void widgetRemoved(QWidget *w);
    void objectRemoved(QObject *o);

public:
    virtual QStringList checkContents() const = 0;
    QStringList activeResourceFilePaths() const;
    virtual QWidget *formContainer() const = 0;
    void activateResourceFilePaths(const QStringList &paths, int *errorCount = 0, QString *errorMessages /Out/ = 0);
};
