// qqmlcomponent.sip generated by MetaSIP
//
// This file is part of the QtQml Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQmlComponent : public QObject
{
%TypeHeaderCode
#include <qqmlcomponent.h>
%End

public:
    enum CompilationMode
    {
        PreferSynchronous,
        Asynchronous,
    };

    QQmlComponent(QQmlEngine *, QObject *parent /TransferThis/ = 0);
    QQmlComponent(QQmlEngine *, const QString &fileName, QObject *parent /TransferThis/ = 0) /ReleaseGIL/;
    QQmlComponent(QQmlEngine *, const QString &fileName, QQmlComponent::CompilationMode mode, QObject *parent /TransferThis/ = 0) /ReleaseGIL/;
    QQmlComponent(QQmlEngine *, const QUrl &url, QObject *parent /TransferThis/ = 0) /ReleaseGIL/;
    QQmlComponent(QQmlEngine *, const QUrl &url, QQmlComponent::CompilationMode mode, QObject *parent /TransferThis/ = 0) /ReleaseGIL/;
%If (Qt_6_5_0 -)
    QQmlComponent(QQmlEngine *engine, QAnyStringView uri, QAnyStringView typeName, QObject *parent /TransferThis/ = 0) /ReleaseGIL/;
%End
%If (Qt_6_5_0 -)
    QQmlComponent(QQmlEngine *engine, QAnyStringView uri, QAnyStringView typeName, QQmlComponent::CompilationMode mode, QObject *parent /TransferThis/ = 0) /ReleaseGIL/;
%End
    QQmlComponent(QObject *parent /TransferThis/ = 0);
    virtual ~QQmlComponent();

    enum Status
    {
        Null,
        Ready,
        Loading,
        Error,
    };

    QQmlComponent::Status status() const;
%If (Qt_6_5_0 -)
    bool isBound() const;
%End
    bool isNull() const;
    bool isReady() const;
    bool isError() const;
    bool isLoading() const;
    QList<QQmlError> errors() const;
    qreal progress() const;
    QUrl url() const;
    virtual QObject *create(QQmlContext *context = 0) /TransferBack/;
    QObject *createWithInitialProperties(const QVariantMap &initialProperties, QQmlContext *context = 0) /TransferBack/;
    virtual QObject *beginCreate(QQmlContext *) /TransferBack/;
    virtual void completeCreate();
    void create(QQmlIncubator &, QQmlContext *context = 0, QQmlContext *forContext = 0);
    QQmlContext *creationContext() const;

public slots:
    void loadUrl(const QUrl &url) /ReleaseGIL/;
    void loadUrl(const QUrl &url, QQmlComponent::CompilationMode mode) /ReleaseGIL/;
    void setData(const QByteArray &, const QUrl &baseUrl);
%If (Qt_6_5_0 -)
    void loadFromModule(QAnyStringView uri, QAnyStringView typeName, QQmlComponent::CompilationMode mode = QQmlComponent::PreferSynchronous);
%End

signals:
    void statusChanged(QQmlComponent::Status);
    void progressChanged(qreal);

public:
    QQmlEngine *engine() const;
    void setInitialProperties(QObject *component, const QVariantMap &properties);
};
