// qopenglfunctions_2_0.sip generated by MetaSIP
//
// This file is part of the QtOpenGL Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (!PyQt_OpenGL_ES2)

class QOpenGLFunctions_2_0 : public QAbstractOpenGLFunctions
{
%TypeHeaderCode
#include <qopenglfunctions_2_0.h>
%End

public:
    QOpenGLFunctions_2_0();
    bool initializeOpenGLFunctions();
    void glViewport(GLint x, GLint y, GLsizei width, GLsizei height);
    void glDepthRange(GLdouble nearVal, GLdouble farVal);
    GLboolean glIsEnabled(GLenum cap);
    void glGetTexLevelParameteriv(GLenum target, GLint level, GLenum pname, SIP_PYOBJECT *params /TypeHint="int"/);
%MethodCode
        GLint params[1];
            
        sipCpp->glGetTexLevelParameteriv(a0, a1, a2, params);
        
        a3 = qpyopengl_from_GLint(&sipIsErr, params, 1);
%End

    void glGetTexLevelParameterfv(GLenum target, GLint level, GLenum pname, SIP_PYOBJECT *params /TypeHint="float"/);
%MethodCode
        GLfloat params[1];
            
        sipCpp->glGetTexLevelParameterfv(a0, a1, a2, params);
        
        a3 = qpyopengl_from_GLfloat(&sipIsErr, params, 1);
%End

    void glGetTexParameteriv(GLenum target, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[int, Tuple[int, int, int, int]]"/);
%MethodCode
        GLint params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        #if defined(GL_TEXTURE_SWIZZLE_RGBA) || defined(GL_TEXTURE_BORDER_COLOR)
        #if defined(GL_TEXTURE_SWIZZLE_RGBA)
        case GL_TEXTURE_SWIZZLE_RGBA:
        #endif
        #if defined(GL_TEXTURE_BORDER_COLOR)
        case GL_TEXTURE_BORDER_COLOR:
        #endif
            nr_params = 4;
            break;
        #endif
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetTexParameteriv(a0, a1, params);
        
        a2 = qpyopengl_from_GLint(&sipIsErr, params, nr_params);
%End

    void glGetTexParameterfv(GLenum target, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[float, Tuple[float, float, float, float]]"/);
%MethodCode
        GLfloat params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        #if defined(GL_TEXTURE_SWIZZLE_RGBA) || defined(GL_TEXTURE_BORDER_COLOR)
        #if defined(GL_TEXTURE_SWIZZLE_RGBA)
        case GL_TEXTURE_SWIZZLE_RGBA:
        #endif
        #if defined(GL_TEXTURE_BORDER_COLOR)
        case GL_TEXTURE_BORDER_COLOR:
        #endif
            nr_params = 4;
            break;
        #endif
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetTexParameterfv(a0, a1, params);
        
        a2 = qpyopengl_from_GLfloat(&sipIsErr, params, nr_params);
%End

    const char *glGetString(GLenum name);
%MethodCode
        sipRes = reinterpret_cast<const char *>(sipCpp->glGetString(a0));
%End

    void glGetIntegerv(GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[int, Tuple[int, ...]]"/);
%MethodCode
        GLint fixed_params[16], *params;
        GLint nr_params;
        GLenum query;
        
        nr_params = qpyopengl_get(a0, &query);
        
        if (nr_params == 0)
        {
            sipCpp->glGetIntegerv(query, &nr_params);
            params = new GLint[nr_params];
        }
        else
        {
            params = fixed_params;
        }
        
        sipCpp->glGetIntegerv(a0, params);
        a1 = qpyopengl_from_GLint(&sipIsErr, params, nr_params);
            
        if (params != fixed_params)
            delete[] params;
%End

    void glGetFloatv(GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[float, Tuple[float, ...]]"/);
%MethodCode
        GLfloat fixed_params[16], *params;
        GLint nr_params;
        GLenum query;
        
        nr_params = qpyopengl_get(a0, &query);
        
        if (nr_params == 0)
        {
            sipCpp->glGetIntegerv(query, &nr_params);
            params = new GLfloat[nr_params];
        }
        else
        {
            params = fixed_params;
        }
        
        sipCpp->glGetFloatv(a0, params);
        a1 = qpyopengl_from_GLfloat(&sipIsErr, params, nr_params);
            
        if (params != fixed_params)
            delete[] params;
%End

    GLenum glGetError();
    void glGetDoublev(GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[float, Tuple[float, ...]]"/);
%MethodCode
        GLdouble fixed_params[16], *params;
        GLint nr_params;
        GLenum query;
        
        nr_params = qpyopengl_get(a0, &query);
        
        if (nr_params == 0)
        {
            sipCpp->glGetIntegerv(query, &nr_params);
            params = new GLdouble[nr_params];
        }
        else
        {
            params = fixed_params;
        }
        
        sipCpp->glGetDoublev(a0, params);
        a1 = qpyopengl_from_GLdouble(&sipIsErr, params, nr_params);
            
        if (params != fixed_params)
            delete[] params;
%End

    void glGetBooleanv(GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[bool, Tuple[bool, ...]]"/);
%MethodCode
        GLboolean fixed_params[16], *params;
        GLint nr_params;
        GLenum query;
        
        nr_params = qpyopengl_get(a0, &query);
        
        if (nr_params == 0)
        {
            sipCpp->glGetIntegerv(query, &nr_params);
            params = new GLboolean[nr_params];
        }
        else
        {
            params = fixed_params;
        }
        
        sipCpp->glGetBooleanv(a0, params);
        a1 = qpyopengl_from_GLboolean(&sipIsErr, params, nr_params);
            
        if (params != fixed_params)
            delete[] params;
%End

    SIP_PYOBJECT glReadPixels(GLint x, GLint y, GLsizei width, GLsizei height, GLenum format, GLenum type) /TypeHint="Union[Tuple[float, ...], Tuple[int, ...]]"/;
%MethodCode
        int components;
        
        switch (a4)
        {
            case GL_BGR:
            case GL_RGB:
            {
                components = 3;
                break;
            }
        
            case GL_BGRA:
            case GL_RGBA:
            {
                components = 4;
                break;
            }
        
            case GL_RED:
            case GL_GREEN:
            case GL_BLUE:
            case GL_ALPHA:
            case GL_DEPTH_COMPONENT:
            case GL_STENCIL_INDEX:
            case GL_DEPTH_STENCIL:
            {
                components = 1;
                break;
            }
        
            default:
                components = 0;
        }
        
        Py_ssize_t length = components * a2 * a3;
        
        switch (a5)
        {
            case GL_FLOAT:
            {
                GLfloat *data = new GLfloat[length];
        
                sipCpp->glReadPixels(a0, a1, a2, a3, a4, a5, data);
                sipRes = qpyopengl_from_GLfloat(&sipIsErr, data, length);
                delete [] data;
        
                break;
            }
        
            case GL_INT:
            {
                GLint *data = new GLint[length];
        
                sipCpp->glReadPixels(a0, a1, a2, a3, a4, a5, data);
                sipRes = qpyopengl_from_GLint(&sipIsErr, data, length);
                delete [] data;
        
                break;
            }
        
            case GL_UNSIGNED_INT:
            case GL_UNSIGNED_INT_8_8_8_8:
            case GL_UNSIGNED_INT_8_8_8_8_REV:
            case GL_UNSIGNED_INT_10_10_10_2:
            case GL_UNSIGNED_INT_2_10_10_10_REV:
            case GL_UNSIGNED_INT_24_8:
            case GL_UNSIGNED_INT_10F_11F_11F_REV:
            case GL_UNSIGNED_INT_5_9_9_9_REV:
            {
                GLuint *data = new GLuint[length];
        
                sipCpp->glReadPixels(a0, a1, a2, a3, a4, a5, data);
                sipRes = qpyopengl_from_GLuint(&sipIsErr, data, length);
                delete [] data;
        
                break;
            }
        
            case GL_SHORT:
            case GL_UNSIGNED_SHORT:
            case GL_UNSIGNED_SHORT_5_6_5:
            case GL_UNSIGNED_SHORT_5_6_5_REV:
            case GL_UNSIGNED_SHORT_4_4_4_4:
            case GL_UNSIGNED_SHORT_4_4_4_4_REV:
            case GL_UNSIGNED_SHORT_5_5_5_1:
            case GL_UNSIGNED_SHORT_1_5_5_5_REV:
            case GL_BYTE:
            case GL_UNSIGNED_BYTE:
            case GL_UNSIGNED_BYTE_3_3_2:
            case GL_UNSIGNED_BYTE_2_3_3_REV:
            case GL_HALF_FLOAT:
            case GL_FLOAT_32_UNSIGNED_INT_24_8_REV:
            default:
                sipIsErr = 1;
                PyErr_SetString(PyExc_ValueError, "pixel data format not supported");
        }
%End

    void glReadBuffer(GLenum mode);
    void glPixelStorei(GLenum pname, GLint param);
    void glPixelStoref(GLenum pname, GLfloat param);
    void glDepthFunc(GLenum func);
    void glStencilOp(GLenum fail, GLenum zfail, GLenum zpass);
    void glStencilFunc(GLenum func, GLint ref, GLuint mask);
    void glLogicOp(GLenum opcode);
    void glBlendFunc(GLenum sfactor, GLenum dfactor);
    void glFlush();
    void glFinish();
    void glEnable(GLenum cap);
    void glDisable(GLenum cap);
    void glDepthMask(GLboolean flag);
    void glColorMask(GLboolean red, GLboolean green, GLboolean blue, GLboolean alpha);
    void glStencilMask(GLuint mask);
    void glClearDepth(GLdouble depth);
    void glClearStencil(GLint s);
    void glClearColor(GLfloat red, GLfloat green, GLfloat blue, GLfloat alpha);
    void glClear(GLbitfield mask);
    void glDrawBuffer(GLenum mode);
    void glTexImage2D(GLenum target, GLint level, GLint internalformat, GLsizei width, GLsizei height, GLint border, GLenum format, GLenum type, SIP_PYOBJECT pixels /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a8, a7, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexImage2D(a0, a1, a2, a3, a4, a5, a6, a7, array);
%End

    void glTexImage1D(GLenum target, GLint level, GLint internalformat, GLsizei width, GLint border, GLenum format, GLenum type, SIP_PYOBJECT pixels /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a7, a6, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexImage1D(a0, a1, a2, a3, a4, a5, a6, array);
%End

    void glTexParameteriv(GLenum target, GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexParameteriv(a0, a1, reinterpret_cast<const GLint *>(array));
%End

    void glTexParameteri(GLenum target, GLenum pname, GLint param);
    void glTexParameterfv(GLenum target, GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexParameterfv(a0, a1, reinterpret_cast<const GLfloat *>(array));
%End

    void glTexParameterf(GLenum target, GLenum pname, GLfloat param);
    void glScissor(GLint x, GLint y, GLsizei width, GLsizei height);
    void glPolygonMode(GLenum face, GLenum mode);
    void glPointSize(GLfloat size);
    void glLineWidth(GLfloat width);
    void glHint(GLenum target, GLenum mode);
    void glFrontFace(GLenum mode);
    void glCullFace(GLenum mode);
    void glIndexubv(SIP_PYOBJECT c /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glIndexubv(reinterpret_cast<const GLubyte *>(array));
%End

    void glIndexub(GLubyte c);
    GLboolean glIsTexture(GLuint texture);
    void glGenTextures(GLsizei n, SIP_PYOBJECT *textures /TypeHint="Union[int, Tuple[int, ...]]"/);
%MethodCode
        GLuint *params = new GLuint[a0];
            
        sipCpp->glGenTextures(a0, params);
        
        a1 = qpyopengl_from_GLuint(&sipIsErr, params, a0);
        
        delete[] params;
%End

    void glDeleteTextures(GLsizei n, SIP_PYOBJECT textures /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_UNSIGNED_INT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glDeleteTextures(a0, reinterpret_cast<const GLuint *>(array));
%End

    void glBindTexture(GLenum target, GLuint texture);
    void glTexSubImage2D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLenum type, SIP_PYOBJECT pixels /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a8, a7, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexSubImage2D(a0, a1, a2, a3, a4, a5, a6, a7, array);
%End

    void glTexSubImage1D(GLenum target, GLint level, GLint xoffset, GLsizei width, GLenum format, GLenum type, SIP_PYOBJECT pixels /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a6, a5, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexSubImage1D(a0, a1, a2, a3, a4, a5, array);
%End

    void glCopyTexSubImage2D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint x, GLint y, GLsizei width, GLsizei height);
    void glCopyTexSubImage1D(GLenum target, GLint level, GLint xoffset, GLint x, GLint y, GLsizei width);
    void glCopyTexImage2D(GLenum target, GLint level, GLenum internalformat, GLint x, GLint y, GLsizei width, GLsizei height, GLint border);
    void glCopyTexImage1D(GLenum target, GLint level, GLenum internalformat, GLint x, GLint y, GLsizei width, GLint border);
    void glPolygonOffset(GLfloat factor, GLfloat units);
    void glDrawElements(GLenum mode, GLsizei count, GLenum type, SIP_PYOBJECT indices /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a3, a2, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glDrawElements(a0, a1, a2, array);
%End

    void glDrawArrays(GLenum mode, GLint first, GLsizei count);
    void glCopyTexSubImage3D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLint x, GLint y, GLsizei width, GLsizei height);
    void glTexSubImage3D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLenum type, SIP_PYOBJECT pixels /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a10, a9, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexSubImage3D(a0, a1, a2, a3, a4, a5, a6, a7, a8, a9, array);
%End

    void glTexImage3D(GLenum target, GLint level, GLint internalformat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLenum format, GLenum type, SIP_PYOBJECT pixels /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a9, a8, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexImage3D(a0, a1, a2, a3, a4, a5, a6, a7, a8, array);
%End

    void glDrawRangeElements(GLenum mode, GLuint start, GLuint end, GLsizei count, GLenum type, SIP_PYOBJECT indices /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a5, a4, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glDrawRangeElements(a0, a1, a2, a3, a4, array);
%End

    void glBlendEquation(GLenum mode);
    void glBlendColor(GLfloat red, GLfloat green, GLfloat blue, GLfloat alpha);
    void glCompressedTexSubImage1D(GLenum target, GLint level, GLint xoffset, GLsizei width, GLenum format, GLsizei imageSize, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a6, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glCompressedTexSubImage1D(a0, a1, a2, a3, a4, a5, array);
%End

    void glCompressedTexSubImage2D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLsizei imageSize, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a8, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glCompressedTexSubImage2D(a0, a1, a2, a3, a4, a5, a6, a7, array);
%End

    void glCompressedTexSubImage3D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLsizei imageSize, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a10, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glCompressedTexSubImage3D(a0, a1, a2, a3, a4, a5, a6, a7, a8, a9,
                array);
%End

    void glCompressedTexImage1D(GLenum target, GLint level, GLenum internalformat, GLsizei width, GLint border, GLsizei imageSize, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a6, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glCompressedTexImage1D(a0, a1, a2, a3, a4, a5, array);
%End

    void glCompressedTexImage2D(GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLint border, GLsizei imageSize, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a7, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glCompressedTexImage2D(a0, a1, a2, a3, a4, a5, a6, array);
%End

    void glCompressedTexImage3D(GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLsizei imageSize, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a8, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glCompressedTexImage3D(a0, a1, a2, a3, a4, a5, a6, a7, array);
%End

    void glSampleCoverage(GLfloat value, GLboolean invert);
    void glActiveTexture(GLenum texture);
    void glPointParameteriv(GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glPointParameteriv(a0, reinterpret_cast<const GLint *>(array));
%End

    void glPointParameteri(GLenum pname, GLint param);
    void glPointParameterfv(GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glPointParameterfv(a0, reinterpret_cast<const GLfloat *>(array));
%End

    void glPointParameterf(GLenum pname, GLfloat param);
    void glBlendFuncSeparate(GLenum sfactorRGB, GLenum dfactorRGB, GLenum sfactorAlpha, GLenum dfactorAlpha);
    void glGetBufferParameteriv(GLenum target, GLenum pname, GLint *params);
    GLboolean glUnmapBuffer(GLenum target);
    void glBufferSubData(GLenum target, GLintptr offset, GLsizeiptr size, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array;
        
        if (a3 == Py_None)
            array = 0;
        else
            array = qpyopengl_value_array(&sipError, a3, GL_UNSIGNED_BYTE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glBufferSubData(a0, a1, a2, array);
%End

    void glBufferData(GLenum target, GLsizeiptr size, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/, GLenum usage);
%MethodCode
        const GLvoid *array;
        
        if (a2 == Py_None)
            array = 0;
        else
            array = qpyopengl_value_array(&sipError, a2, GL_UNSIGNED_BYTE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glBufferData(a0, a1, array, a3);
%End

    GLboolean glIsBuffer(GLuint buffer);
    void glGenBuffers(GLsizei n, SIP_PYOBJECT *buffers /TypeHint="Union[int, Tuple[int, ...]]"/);
%MethodCode
        GLuint *params = new GLuint[a0];
            
        sipCpp->glGenBuffers(a0, params);
        
        a1 = qpyopengl_from_GLuint(&sipIsErr, params, a0);
        
        delete[] params;
%End

    void glDeleteBuffers(GLsizei n, SIP_PYOBJECT buffers /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_UNSIGNED_INT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glDeleteBuffers(a0, reinterpret_cast<const GLuint *>(array));
%End

    void glBindBuffer(GLenum target, GLuint buffer);
    void glGetQueryiv(GLenum target, GLenum pname, GLint *params);
    void glEndQuery(GLenum target);
    void glBeginQuery(GLenum target, GLuint id);
    GLboolean glIsQuery(GLuint id);
    void glDeleteQueries(GLsizei n, SIP_PYOBJECT ids /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_UNSIGNED_INT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glDeleteQueries(a0, reinterpret_cast<const GLuint *>(array));
%End

    void glGenQueries(GLsizei n, SIP_PYOBJECT *ids /TypeHint="Union[int, Tuple[int, ...]]"/);
%MethodCode
        GLuint *params = new GLuint[a0];
            
        sipCpp->glGenQueries(a0, params);
        
        a1 = qpyopengl_from_GLuint(&sipIsErr, params, a0);
        
        delete[] params;
%End

    void glVertexAttribPointer(GLuint index, GLint size, GLenum type, GLboolean normalized, GLsizei stride, SIP_PYOBJECT pointer /TypeHint="PYQT_OPENGL_BOUND_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array_cached(&sipError, a5, a2, sipSelf,
                "VertexAttribPointer", a0);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttribPointer(a0, a1, a2, a3, a4, array);
%End

    void glValidateProgram(GLuint program);
    void glUniformMatrix4fv(GLint location, GLsizei count, GLboolean transpose, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a3, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniformMatrix4fv(a0, a1, a2,
                    reinterpret_cast<const GLfloat *>(array));
%End

    void glUniformMatrix3fv(GLint location, GLsizei count, GLboolean transpose, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a3, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniformMatrix3fv(a0, a1, a2,
                    reinterpret_cast<const GLfloat *>(array));
%End

    void glUniformMatrix2fv(GLint location, GLsizei count, GLboolean transpose, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a3, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniformMatrix2fv(a0, a1, a2,
                    reinterpret_cast<const GLfloat *>(array));
%End

    void glUniform4iv(GLint location, GLsizei count, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform4iv(a0, a1, reinterpret_cast<const GLint *>(array));
%End

    void glUniform3iv(GLint location, GLsizei count, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform3iv(a0, a1, reinterpret_cast<const GLint *>(array));
%End

    void glUniform2iv(GLint location, GLsizei count, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform2iv(a0, a1, reinterpret_cast<const GLint *>(array));
%End

    void glUniform1iv(GLint location, GLsizei count, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform1iv(a0, a1, reinterpret_cast<const GLint *>(array));
%End

    void glUniform4fv(GLint location, GLsizei count, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform4fv(a0, a1, reinterpret_cast<const GLfloat *>(array));
%End

    void glUniform3fv(GLint location, GLsizei count, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform3fv(a0, a1, reinterpret_cast<const GLfloat *>(array));
%End

    void glUniform2fv(GLint location, GLsizei count, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform2fv(a0, a1, reinterpret_cast<const GLfloat *>(array));
%End

    void glUniform1fv(GLint location, GLsizei count, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform1fv(a0, a1, reinterpret_cast<const GLfloat *>(array));
%End

    void glUniform4i(GLint location, GLint v0, GLint v1, GLint v2, GLint v3);
    void glUniform3i(GLint location, GLint v0, GLint v1, GLint v2);
    void glUniform2i(GLint location, GLint v0, GLint v1);
    void glUniform1i(GLint location, GLint v0);
    void glUniform4f(GLint location, GLfloat v0, GLfloat v1, GLfloat v2, GLfloat v3);
    void glUniform3f(GLint location, GLfloat v0, GLfloat v1, GLfloat v2);
    void glUniform2f(GLint location, GLfloat v0, GLfloat v1);
    void glUniform1f(GLint location, GLfloat v0);
    void glUseProgram(GLuint program);
    void glLinkProgram(GLuint program);
    GLboolean glIsShader(GLuint shader);
    GLboolean glIsProgram(GLuint program);
    void glGetVertexAttribiv(GLuint index, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[int, Tuple[int, int, int, int]]"/);
%MethodCode
        GLint params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_CURRENT_VERTEX_ATTRIB:
            nr_params = 4;
            break;
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetVertexAttribiv(a0, a1, params);
        
        a2 = qpyopengl_from_GLint(&sipIsErr, params, nr_params);
%End

    void glGetVertexAttribfv(GLuint index, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[float, Tuple[float, float, float, float]]"/);
%MethodCode
        GLfloat params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_CURRENT_VERTEX_ATTRIB:
            nr_params = 4;
            break;
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetVertexAttribfv(a0, a1, params);
        
        a2 = qpyopengl_from_GLfloat(&sipIsErr, params, nr_params);
%End

    void glGetVertexAttribdv(GLuint index, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[float, Tuple[float, float, float, float]]"/);
%MethodCode
        GLdouble params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_CURRENT_VERTEX_ATTRIB:
            nr_params = 4;
            break;
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetVertexAttribdv(a0, a1, params);
        
        a2 = qpyopengl_from_GLdouble(&sipIsErr, params, nr_params);
%End

    GLint glGetUniformLocation(GLuint program, const GLchar *name);
    SIP_PYOBJECT glGetShaderSource(GLuint shader) /TypeHint="bytes"/;
%MethodCode
        GLint bufsize;
        
        sipCpp->glGetShaderiv(a0, GL_SHADER_SOURCE_LENGTH, &bufsize);
        
        if (bufsize > 0)
        {
            GLchar *source = new GLchar[bufsize];
        
            sipCpp->glGetShaderSource(a0, bufsize, 0, source);
            sipRes = PyBytes_FromString(source);
        
            delete[] source;
        }
        else
        {
            sipRes = PyBytes_FromString("");
        }
%End

    SIP_PYOBJECT glGetShaderInfoLog(GLuint shader) /TypeHint="bytes"/;
%MethodCode
        GLint bufsize;
        
        sipCpp->glGetShaderiv(a0, GL_INFO_LOG_LENGTH, &bufsize);
        
        if (bufsize > 0)
        {
            GLchar *log = new GLchar[bufsize];
        
            sipCpp->glGetShaderInfoLog(a0, bufsize, 0, log);
            sipRes = PyBytes_FromString(log);
        
            delete[] log;
        }
        else
        {
            sipRes = PyBytes_FromString("");
        }
%End

    void glGetShaderiv(GLuint shader, GLenum pname, GLint *params);
    SIP_PYOBJECT glGetProgramInfoLog(GLuint program) /TypeHint="bytes"/;
%MethodCode
        GLint bufsize;
        
        sipCpp->glGetProgramiv(a0, GL_INFO_LOG_LENGTH, &bufsize);
        
        if (bufsize > 0)
        {
            GLchar *log = new GLchar[bufsize];
        
            sipCpp->glGetProgramInfoLog(a0, bufsize, 0, log);
            sipRes = PyBytes_FromString(log);
        
            delete[] log;
        }
        else
        {
            sipRes = PyBytes_FromString("");
        }
%End

    void glGetProgramiv(GLuint program, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[int, Tuple[int, int, int]]"/);
%MethodCode
        GLint params[3];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        #if defined(GL_COMPUTE_LOCAL_WORK_SIZE)
        case GL_COMPUTE_LOCAL_WORK_SIZE:
            nr_params = 3;
            break;
        #endif
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetProgramiv(a0, a1, params);
        
        a2 = qpyopengl_from_GLint(&sipIsErr, params, nr_params);
%End

    GLint glGetAttribLocation(GLuint program, const GLchar *name);
    SIP_PYOBJECT glGetAttachedShaders(GLuint program) /TypeHint="Tuple[int, ...]"/;
%MethodCode
        GLint nr_shaders;
        
        sipCpp->glGetProgramiv(a0, GL_ATTACHED_SHADERS, &nr_shaders);
        
        if (nr_shaders < 1)
        {
            sipRes = PyTuple_New(0);
        }
        else
        {
            GLuint *shaders = new GLuint[nr_shaders];
        
            sipCpp->glGetAttachedShaders(a0, nr_shaders, 0, shaders);
        
            sipRes = PyTuple_New(nr_shaders);
        
            if (sipRes)
            {
                for (GLint i = 0; i < nr_shaders; ++i)
                {
                    PyObject *itm = PyLong_FromLong(shaders[i]);
                    
                    if (!itm)
                    {
                        Py_DECREF(sipRes);
                        sipRes = 0;
                        break;
                    }
                    
                    PyTuple_SetItem(sipRes, i, itm);
                }
            }
        
            delete[] shaders;
        }
        
        if (!sipRes)
            sipIsErr = 1;
%End

    SIP_PYOBJECT glGetActiveUniform(GLuint program, GLuint index) /TypeHint="Tuple[str, int, int]"/;
%MethodCode
        GLint bufsize;
        
        sipCpp->glGetProgramiv(a0, GL_ACTIVE_UNIFORM_MAX_LENGTH, &bufsize);
        
        GLchar *name = new GLchar[bufsize];
        GLint size;
        GLenum type;
        
        sipCpp->glGetActiveUniform(a0, a1, bufsize, 0, &size, &type, name);
        
        sipRes = Py_BuildValue("siI", name, size, type);
        
        if (!sipRes)
            sipIsErr = 1;
        
        delete[] name;
%End

    SIP_PYOBJECT glGetActiveAttrib(GLuint program, GLuint index) /TypeHint="Tuple[str, int, int]"/;
%MethodCode
        GLint bufsize;
        
        sipCpp->glGetProgramiv(a0, GL_ACTIVE_ATTRIBUTE_MAX_LENGTH, &bufsize);
        
        GLchar *name = new GLchar[bufsize];
        GLint size;
        GLenum type;
        
        sipCpp->glGetActiveAttrib(a0, a1, bufsize, 0, &size, &type, name);
        
        sipRes = Py_BuildValue("siI", name, size, type);
        
        if (!sipRes)
            sipIsErr = 1;
        
        delete[] name;
%End

    void glEnableVertexAttribArray(GLuint index);
    void glDisableVertexAttribArray(GLuint index);
    void glDetachShader(GLuint program, GLuint shader);
    void glDeleteShader(GLuint shader);
    void glDeleteProgram(GLuint program);
    GLuint glCreateShader(GLenum type);
    GLuint glCreateProgram();
    void glCompileShader(GLuint shader);
    void glBindAttribLocation(GLuint program, GLuint index, const GLchar *name);
    void glAttachShader(GLuint program, GLuint shader);
    void glStencilMaskSeparate(GLenum face, GLuint mask);
    void glStencilFuncSeparate(GLenum face, GLenum func, GLint ref, GLuint mask);
    void glStencilOpSeparate(GLenum face, GLenum sfail, GLenum dpfail, GLenum dppass);
    void glDrawBuffers(GLsizei n, SIP_PYOBJECT bufs /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_UNSIGNED_INT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glDrawBuffers(a0, reinterpret_cast<const GLenum *>(array));
%End

    void glBlendEquationSeparate(GLenum modeRGB, GLenum modeAlpha);
    void glTranslatef(GLfloat x, GLfloat y, GLfloat z);
    void glTranslated(GLdouble x, GLdouble y, GLdouble z);
    void glScalef(GLfloat x, GLfloat y, GLfloat z);
    void glScaled(GLdouble x, GLdouble y, GLdouble z);
    void glRotatef(GLfloat angle, GLfloat x, GLfloat y, GLfloat z);
    void glRotated(GLdouble angle, GLdouble x, GLdouble y, GLdouble z);
    void glPushMatrix();
    void glPopMatrix();
    void glOrtho(GLdouble left, GLdouble right, GLdouble bottom, GLdouble top, GLdouble zNear, GLdouble zFar);
    void glMultMatrixd(SIP_PYOBJECT m /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMultMatrixd(reinterpret_cast<const GLdouble *>(array));
%End

    void glMultMatrixf(SIP_PYOBJECT m /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMultMatrixf(reinterpret_cast<const GLfloat *>(array));
%End

    void glMatrixMode(GLenum mode);
    void glLoadMatrixd(SIP_PYOBJECT m /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glLoadMatrixd(reinterpret_cast<const GLdouble *>(array));
%End

    void glLoadMatrixf(SIP_PYOBJECT m /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glLoadMatrixf(reinterpret_cast<const GLfloat *>(array));
%End

    void glLoadIdentity();
    void glFrustum(GLdouble left, GLdouble right, GLdouble bottom, GLdouble top, GLdouble zNear, GLdouble zFar);
    GLboolean glIsList(GLuint list);
    void glGetTexGeniv(GLenum coord, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[int, Tuple[int, int, int, int]]"/);
%MethodCode
        GLint params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_OBJECT_PLANE:
        case GL_EYE_PLANE:
            nr_params = 4;
            break;
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetTexGeniv(a0, a1, params);
        
        a2 = qpyopengl_from_GLint(&sipIsErr, params, nr_params);
%End

    void glGetTexGenfv(GLenum coord, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[float, Tuple[float, float, float, float]]"/);
%MethodCode
        GLfloat params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_OBJECT_PLANE:
        case GL_EYE_PLANE:
            nr_params = 4;
            break;
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetTexGenfv(a0, a1, params);
        
        a2 = qpyopengl_from_GLfloat(&sipIsErr, params, nr_params);
%End

    void glGetTexGendv(GLenum coord, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[float, Tuple[float, float, float, float]]"/);
%MethodCode
        GLdouble params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_OBJECT_PLANE:
        case GL_EYE_PLANE:
            nr_params = 4;
            break;
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetTexGendv(a0, a1, params);
        
        a2 = qpyopengl_from_GLdouble(&sipIsErr, params, nr_params);
%End

    void glGetTexEnviv(GLenum target, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[int, Tuple[int, int, int, int]]"/);
%MethodCode
        GLint params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_TEXTURE_ENV_COLOR:
            nr_params = 4;
            break;
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetTexEnviv(a0, a1, params);
        
        a2 = qpyopengl_from_GLint(&sipIsErr, params, nr_params);
%End

    void glGetTexEnvfv(GLenum target, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[float, Tuple[float, float, float, float]]"/);
%MethodCode
        GLfloat params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_TEXTURE_ENV_COLOR:
            nr_params = 4;
            break;
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetTexEnvfv(a0, a1, params);
        
        a2 = qpyopengl_from_GLfloat(&sipIsErr, params, nr_params);
%End

    void glGetMaterialiv(GLenum face, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[int, Tuple[int, int, int], Tuple[int, int, int, int]]"/);
%MethodCode
        GLint params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_SHININESS:
            nr_params = 1;
            break;
        
        case GL_COLOR_INDEXES:
            nr_params = 3;
            break;
        
        default:
            nr_params = 4;
        }
        
        sipCpp->glGetMaterialiv(a0, a1, params);
        
        a2 = qpyopengl_from_GLint(&sipIsErr, params, nr_params);
%End

    void glGetMaterialfv(GLenum face, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[float, Tuple[float, float, float], Tuple[float, float, float, float]]"/);
%MethodCode
        GLfloat params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_SHININESS:
            nr_params = 1;
            break;
        
        case GL_COLOR_INDEXES:
            nr_params = 3;
            break;
        
        default:
            nr_params = 4;
        }
        
        sipCpp->glGetMaterialfv(a0, a1, params);
        
        a2 = qpyopengl_from_GLfloat(&sipIsErr, params, nr_params);
%End

    void glGetLightiv(GLenum light, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[int, Tuple[int, int, int], Tuple[int, int, int, int]]"/);
%MethodCode
        GLint params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_AMBIENT:
        case GL_DIFFUSE:
        case GL_SPECULAR:
        case GL_POSITION:
            nr_params = 4;
            break;
        
        case GL_SPOT_DIRECTION:
            nr_params = 3;
            break;
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetLightiv(a0, a1, params);
        
        a2 = qpyopengl_from_GLint(&sipIsErr, params, nr_params);
%End

    void glGetLightfv(GLenum light, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[float, Tuple[float, float, float], Tuple[float, float, float, float]]"/);
%MethodCode
        GLfloat params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_AMBIENT:
        case GL_DIFFUSE:
        case GL_SPECULAR:
        case GL_POSITION:
            nr_params = 4;
            break;
        
        case GL_SPOT_DIRECTION:
            nr_params = 3;
            break;
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetLightfv(a0, a1, params);
        
        a2 = qpyopengl_from_GLfloat(&sipIsErr, params, nr_params);
%End

    void glGetClipPlane(GLenum plane, SIP_PYOBJECT *equation /TypeHint="Tuple[float, float, float, float]"/);
%MethodCode
        GLdouble params[4];
        
        sipCpp->glGetClipPlane(a0, params);
        
        a1 = qpyopengl_from_GLdouble(&sipIsErr, params, 4);
%End

    void glDrawPixels(GLsizei width, GLsizei height, GLenum format, GLenum type, SIP_PYOBJECT pixels /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a4, a3, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glDrawPixels(a0, a1, a2, a3, array);
%End

    void glCopyPixels(GLint x, GLint y, GLsizei width, GLsizei height, GLenum type);
    void glPixelMapusv(GLenum map, GLint mapsize, SIP_PYOBJECT values /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_UNSIGNED_SHORT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glPixelMapusv(a0, a1, reinterpret_cast<const GLushort *>(array));
%End

    void glPixelMapuiv(GLenum map, GLint mapsize, SIP_PYOBJECT values /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_UNSIGNED_INT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glPixelMapuiv(a0, a1, reinterpret_cast<const GLuint *>(array));
%End

    void glPixelMapfv(GLenum map, GLint mapsize, SIP_PYOBJECT values /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glPixelMapfv(a0, a1, reinterpret_cast<const GLfloat *>(array));
%End

    void glPixelTransferi(GLenum pname, GLint param);
    void glPixelTransferf(GLenum pname, GLfloat param);
    void glPixelZoom(GLfloat xfactor, GLfloat yfactor);
    void glAlphaFunc(GLenum func, GLfloat ref);
    void glEvalPoint2(GLint i, GLint j);
    void glEvalMesh2(GLenum mode, GLint i1, GLint i2, GLint j1, GLint j2);
    void glEvalPoint1(GLint i);
    void glEvalMesh1(GLenum mode, GLint i1, GLint i2);
    void glEvalCoord2fv(SIP_PYOBJECT u /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glEvalCoord2fv(reinterpret_cast<const GLfloat *>(array));
%End

    void glEvalCoord2f(GLfloat u, GLfloat v);
    void glEvalCoord2dv(SIP_PYOBJECT u /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glEvalCoord2dv(reinterpret_cast<const GLdouble *>(array));
%End

    void glEvalCoord2d(GLdouble u, GLdouble v);
    void glEvalCoord1fv(SIP_PYOBJECT u /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glEvalCoord1fv(reinterpret_cast<const GLfloat *>(array));
%End

    void glEvalCoord1f(GLfloat u);
    void glEvalCoord1dv(SIP_PYOBJECT u /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glEvalCoord1dv(reinterpret_cast<const GLdouble *>(array));
%End

    void glEvalCoord1d(GLdouble u);
    void glMapGrid2f(GLint un, GLfloat u1, GLfloat u2, GLint vn, GLfloat v1, GLfloat v2);
    void glMapGrid2d(GLint un, GLdouble u1, GLdouble u2, GLint vn, GLdouble v1, GLdouble v2);
    void glMapGrid1f(GLint un, GLfloat u1, GLfloat u2);
    void glMapGrid1d(GLint un, GLdouble u1, GLdouble u2);
    void glMap2f(GLenum target, GLfloat u1, GLfloat u2, GLint ustride, GLint uorder, GLfloat v1, GLfloat v2, GLint vstride, GLint vorder, SIP_PYOBJECT points /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a9, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMap2f(a0, a1, a2, a3, a4, a5, a6, a7, a8,
                    reinterpret_cast<const GLfloat *>(array));
%End

    void glMap2d(GLenum target, GLdouble u1, GLdouble u2, GLint ustride, GLint uorder, GLdouble v1, GLdouble v2, GLint vstride, GLint vorder, SIP_PYOBJECT points /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a9, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMap2d(a0, a1, a2, a3, a4, a5, a6, a7, a8,
                    reinterpret_cast<const GLdouble *>(array));
%End

    void glMap1f(GLenum target, GLfloat u1, GLfloat u2, GLint stride, GLint order, SIP_PYOBJECT points /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a5, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMap1f(a0, a1, a2, a3, a4,
                    reinterpret_cast<const GLfloat *>(array));
%End

    void glMap1d(GLenum target, GLdouble u1, GLdouble u2, GLint stride, GLint order, SIP_PYOBJECT points /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a5, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMap1d(a0, a1, a2, a3, a4,
                    reinterpret_cast<const GLdouble *>(array));
%End

    void glPushAttrib(GLbitfield mask);
    void glPopAttrib();
    void glAccum(GLenum op, GLfloat value);
    void glIndexMask(GLuint mask);
    void glClearIndex(GLfloat c);
    void glClearAccum(GLfloat red, GLfloat green, GLfloat blue, GLfloat alpha);
    void glPushName(GLuint name);
    void glPopName();
    void glPassThrough(GLfloat token);
    void glLoadName(GLuint name);
    void glInitNames();
    GLint glRenderMode(GLenum mode);
    void glTexGeniv(GLenum coord, GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexGeniv(a0, a1, reinterpret_cast<const GLint *>(array));
%End

    void glTexGeni(GLenum coord, GLenum pname, GLint param);
    void glTexGenfv(GLenum coord, GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexGenfv(a0, a1, reinterpret_cast<const GLfloat *>(array));
%End

    void glTexGenf(GLenum coord, GLenum pname, GLfloat param);
    void glTexGendv(GLenum coord, GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexGendv(a0, a1, reinterpret_cast<const GLdouble *>(array));
%End

    void glTexGend(GLenum coord, GLenum pname, GLdouble param);
    void glTexEnviv(GLenum target, GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexEnviv(a0, a1, reinterpret_cast<const GLint *>(array));
%End

    void glTexEnvi(GLenum target, GLenum pname, GLint param);
    void glTexEnvfv(GLenum target, GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexEnvfv(a0, a1, reinterpret_cast<const GLfloat *>(array));
%End

    void glTexEnvf(GLenum target, GLenum pname, GLfloat param);
    void glShadeModel(GLenum mode);
    void glPolygonStipple(SIP_PYOBJECT mask /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glPolygonStipple(reinterpret_cast<const GLubyte *>(array));
%End

    void glMaterialiv(GLenum face, GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMaterialiv(a0, a1, reinterpret_cast<const GLint *>(array));
%End

    void glMateriali(GLenum face, GLenum pname, GLint param);
    void glMaterialfv(GLenum face, GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMaterialfv(a0, a1, reinterpret_cast<const GLfloat *>(array));
%End

    void glMaterialf(GLenum face, GLenum pname, GLfloat param);
    void glLineStipple(GLint factor, GLushort pattern);
    void glLightModeliv(GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glLightModeliv(a0, reinterpret_cast<const GLint *>(array));
%End

    void glLightModeli(GLenum pname, GLint param);
    void glLightModelfv(GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glLightModelfv(a0, reinterpret_cast<const GLfloat *>(array));
%End

    void glLightModelf(GLenum pname, GLfloat param);
    void glLightiv(GLenum light, GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glLightiv(a0, a1, reinterpret_cast<const GLint *>(array));
%End

    void glLighti(GLenum light, GLenum pname, GLint param);
    void glLightfv(GLenum light, GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glLightfv(a0, a1, reinterpret_cast<const GLfloat *>(array));
%End

    void glLightf(GLenum light, GLenum pname, GLfloat param);
    void glFogiv(GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glFogiv(a0, reinterpret_cast<const GLint *>(array));
%End

    void glFogi(GLenum pname, GLint param);
    void glFogfv(GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glFogfv(a0, reinterpret_cast<const GLfloat *>(array));
%End

    void glFogf(GLenum pname, GLfloat param);
    void glColorMaterial(GLenum face, GLenum mode);
    void glClipPlane(GLenum plane, SIP_PYOBJECT equation /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glClipPlane(a0, reinterpret_cast<const GLdouble *>(array));
%End

    void glVertex4sv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertex4sv(reinterpret_cast<const GLshort *>(array));
%End

    void glVertex4s(GLshort x, GLshort y, GLshort z, GLshort w);
    void glVertex4iv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertex4iv(reinterpret_cast<const GLint *>(array));
%End

    void glVertex4i(GLint x, GLint y, GLint z, GLint w);
    void glVertex4fv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertex4fv(reinterpret_cast<const GLfloat *>(array));
%End

    void glVertex4f(GLfloat x, GLfloat y, GLfloat z, GLfloat w);
    void glVertex4dv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertex4dv(reinterpret_cast<const GLdouble *>(array));
%End

    void glVertex4d(GLdouble x, GLdouble y, GLdouble z, GLdouble w);
    void glVertex3sv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertex3sv(reinterpret_cast<const GLshort *>(array));
%End

    void glVertex3s(GLshort x, GLshort y, GLshort z);
    void glVertex3iv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertex3iv(reinterpret_cast<const GLint *>(array));
%End

    void glVertex3i(GLint x, GLint y, GLint z);
    void glVertex3fv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertex3fv(reinterpret_cast<const GLfloat *>(array));
%End

    void glVertex3f(GLfloat x, GLfloat y, GLfloat z);
    void glVertex3dv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertex3dv(reinterpret_cast<const GLdouble *>(array));
%End

    void glVertex3d(GLdouble x, GLdouble y, GLdouble z);
    void glVertex2sv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertex2sv(reinterpret_cast<const GLshort *>(array));
%End

    void glVertex2s(GLshort x, GLshort y);
    void glVertex2iv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertex2iv(reinterpret_cast<const GLint *>(array));
%End

    void glVertex2i(GLint x, GLint y);
    void glVertex2fv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertex2fv(reinterpret_cast<const GLfloat *>(array));
%End

    void glVertex2f(GLfloat x, GLfloat y);
    void glVertex2dv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertex2dv(reinterpret_cast<const GLdouble *>(array));
%End

    void glVertex2d(GLdouble x, GLdouble y);
    void glTexCoord4sv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexCoord4sv(reinterpret_cast<const GLshort *>(array));
%End

    void glTexCoord4s(GLshort s, GLshort t, GLshort r, GLshort q);
    void glTexCoord4iv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexCoord4iv(reinterpret_cast<const GLint *>(array));
%End

    void glTexCoord4i(GLint s, GLint t, GLint r, GLint q);
    void glTexCoord4fv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexCoord4fv(reinterpret_cast<const GLfloat *>(array));
%End

    void glTexCoord4f(GLfloat s, GLfloat t, GLfloat r, GLfloat q);
    void glTexCoord4dv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexCoord4dv(reinterpret_cast<const GLdouble *>(array));
%End

    void glTexCoord4d(GLdouble s, GLdouble t, GLdouble r, GLdouble q);
    void glTexCoord3sv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexCoord3sv(reinterpret_cast<const GLshort *>(array));
%End

    void glTexCoord3s(GLshort s, GLshort t, GLshort r);
    void glTexCoord3iv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexCoord3iv(reinterpret_cast<const GLint *>(array));
%End

    void glTexCoord3i(GLint s, GLint t, GLint r);
    void glTexCoord3fv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexCoord3fv(reinterpret_cast<const GLfloat *>(array));
%End

    void glTexCoord3f(GLfloat s, GLfloat t, GLfloat r);
    void glTexCoord3dv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexCoord3dv(reinterpret_cast<const GLdouble *>(array));
%End

    void glTexCoord3d(GLdouble s, GLdouble t, GLdouble r);
    void glTexCoord2sv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexCoord2sv(reinterpret_cast<const GLshort *>(array));
%End

    void glTexCoord2s(GLshort s, GLshort t);
    void glTexCoord2iv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexCoord2iv(reinterpret_cast<const GLint *>(array));
%End

    void glTexCoord2i(GLint s, GLint t);
    void glTexCoord2fv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexCoord2fv(reinterpret_cast<const GLfloat *>(array));
%End

    void glTexCoord2f(GLfloat s, GLfloat t);
    void glTexCoord2dv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexCoord2dv(reinterpret_cast<const GLdouble *>(array));
%End

    void glTexCoord2d(GLdouble s, GLdouble t);
    void glTexCoord1sv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexCoord1sv(reinterpret_cast<const GLshort *>(array));
%End

    void glTexCoord1s(GLshort s);
    void glTexCoord1iv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexCoord1iv(reinterpret_cast<const GLint *>(array));
%End

    void glTexCoord1i(GLint s);
    void glTexCoord1fv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexCoord1fv(reinterpret_cast<const GLfloat *>(array));
%End

    void glTexCoord1f(GLfloat s);
    void glTexCoord1dv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexCoord1dv(reinterpret_cast<const GLdouble *>(array));
%End

    void glTexCoord1d(GLdouble s);
    void glRects(GLshort x1, GLshort y1, GLshort x2, GLshort y2);
    void glRecti(GLint x1, GLint y1, GLint x2, GLint y2);
    void glRectf(GLfloat x1, GLfloat y1, GLfloat x2, GLfloat y2);
    void glRectd(GLdouble x1, GLdouble y1, GLdouble x2, GLdouble y2);
    void glRasterPos4sv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glRasterPos4sv(reinterpret_cast<const GLshort *>(array));
%End

    void glRasterPos4s(GLshort x, GLshort y, GLshort z, GLshort w);
    void glRasterPos4iv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glRasterPos4iv(reinterpret_cast<const GLint *>(array));
%End

    void glRasterPos4i(GLint x, GLint y, GLint z, GLint w);
    void glRasterPos4fv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glRasterPos4fv(reinterpret_cast<const GLfloat *>(array));
%End

    void glRasterPos4f(GLfloat x, GLfloat y, GLfloat z, GLfloat w);
    void glRasterPos4dv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glRasterPos4dv(reinterpret_cast<const GLdouble *>(array));
%End

    void glRasterPos4d(GLdouble x, GLdouble y, GLdouble z, GLdouble w);
    void glRasterPos3sv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glRasterPos3sv(reinterpret_cast<const GLshort *>(array));
%End

    void glRasterPos3s(GLshort x, GLshort y, GLshort z);
    void glRasterPos3iv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glRasterPos3iv(reinterpret_cast<const GLint *>(array));
%End

    void glRasterPos3i(GLint x, GLint y, GLint z);
    void glRasterPos3fv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glRasterPos3fv(reinterpret_cast<const GLfloat *>(array));
%End

    void glRasterPos3f(GLfloat x, GLfloat y, GLfloat z);
    void glRasterPos3dv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glRasterPos3dv(reinterpret_cast<const GLdouble *>(array));
%End

    void glRasterPos3d(GLdouble x, GLdouble y, GLdouble z);
    void glRasterPos2sv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glRasterPos2sv(reinterpret_cast<const GLshort *>(array));
%End

    void glRasterPos2s(GLshort x, GLshort y);
    void glRasterPos2iv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glRasterPos2iv(reinterpret_cast<const GLint *>(array));
%End

    void glRasterPos2i(GLint x, GLint y);
    void glRasterPos2fv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glRasterPos2fv(reinterpret_cast<const GLfloat *>(array));
%End

    void glRasterPos2f(GLfloat x, GLfloat y);
    void glRasterPos2dv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glRasterPos2dv(reinterpret_cast<const GLdouble *>(array));
%End

    void glRasterPos2d(GLdouble x, GLdouble y);
    void glNormal3sv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glNormal3sv(reinterpret_cast<const GLshort *>(array));
%End

    void glNormal3s(GLshort nx, GLshort ny, GLshort nz);
    void glNormal3iv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glNormal3iv(reinterpret_cast<const GLint *>(array));
%End

    void glNormal3i(GLint nx, GLint ny, GLint nz);
    void glNormal3fv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glNormal3fv(reinterpret_cast<const GLfloat *>(array));
%End

    void glNormal3f(GLfloat nx, GLfloat ny, GLfloat nz);
    void glNormal3dv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glNormal3dv(reinterpret_cast<const GLdouble *>(array));
%End

    void glNormal3d(GLdouble nx, GLdouble ny, GLdouble nz);
    void glNormal3bv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_BYTE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glNormal3bv(reinterpret_cast<const GLbyte *>(array));
%End

    void glNormal3b(GLbyte nx, GLbyte ny, GLbyte nz);
    void glIndexsv(SIP_PYOBJECT c /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glIndexsv(reinterpret_cast<const GLshort *>(array));
%End

    void glIndexs(GLshort c);
    void glIndexiv(SIP_PYOBJECT c /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glIndexiv(reinterpret_cast<const GLint *>(array));
%End

    void glIndexi(GLint c);
    void glIndexfv(SIP_PYOBJECT c /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glIndexfv(reinterpret_cast<const GLfloat *>(array));
%End

    void glIndexf(GLfloat c);
    void glIndexdv(SIP_PYOBJECT c /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glIndexdv(reinterpret_cast<const GLdouble *>(array));
%End

    void glIndexd(GLdouble c);
    void glEnd();
    void glEdgeFlagv(SIP_PYOBJECT flag /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glEdgeFlagv(reinterpret_cast<const GLboolean *>(array));
%End

    void glEdgeFlag(GLboolean flag);
    void glColor4usv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_UNSIGNED_SHORT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glColor4usv(reinterpret_cast<const GLushort *>(array));
%End

    void glColor4us(GLushort red, GLushort green, GLushort blue, GLushort alpha);
    void glColor4uiv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_UNSIGNED_INT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glColor4uiv(reinterpret_cast<const GLuint *>(array));
%End

    void glColor4ui(GLuint red, GLuint green, GLuint blue, GLuint alpha);
    void glColor4ubv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glColor4ubv(reinterpret_cast<const GLubyte *>(array));
%End

    void glColor4ub(GLubyte red, GLubyte green, GLubyte blue, GLubyte alpha);
    void glColor4sv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glColor4sv(reinterpret_cast<const GLshort *>(array));
%End

    void glColor4s(GLshort red, GLshort green, GLshort blue, GLshort alpha);
    void glColor4iv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glColor4iv(reinterpret_cast<const GLint *>(array));
%End

    void glColor4i(GLint red, GLint green, GLint blue, GLint alpha);
    void glColor4fv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glColor4fv(reinterpret_cast<const GLfloat *>(array));
%End

    void glColor4f(GLfloat red, GLfloat green, GLfloat blue, GLfloat alpha);
    void glColor4dv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glColor4dv(reinterpret_cast<const GLdouble *>(array));
%End

    void glColor4d(GLdouble red, GLdouble green, GLdouble blue, GLdouble alpha);
    void glColor4bv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_BYTE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glColor4bv(reinterpret_cast<const GLbyte *>(array));
%End

    void glColor4b(GLbyte red, GLbyte green, GLbyte blue, GLbyte alpha);
    void glColor3usv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_UNSIGNED_SHORT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glColor3usv(reinterpret_cast<const GLushort *>(array));
%End

    void glColor3us(GLushort red, GLushort green, GLushort blue);
    void glColor3uiv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_UNSIGNED_INT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glColor3uiv(reinterpret_cast<const GLuint *>(array));
%End

    void glColor3ui(GLuint red, GLuint green, GLuint blue);
    void glColor3ubv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glColor3ubv(reinterpret_cast<const GLubyte *>(array));
%End

    void glColor3ub(GLubyte red, GLubyte green, GLubyte blue);
    void glColor3sv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glColor3sv(reinterpret_cast<const GLshort *>(array));
%End

    void glColor3s(GLshort red, GLshort green, GLshort blue);
    void glColor3iv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glColor3iv(reinterpret_cast<const GLint *>(array));
%End

    void glColor3i(GLint red, GLint green, GLint blue);
    void glColor3fv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glColor3fv(reinterpret_cast<const GLfloat *>(array));
%End

    void glColor3f(GLfloat red, GLfloat green, GLfloat blue);
    void glColor3dv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glColor3dv(reinterpret_cast<const GLdouble *>(array));
%End

    void glColor3d(GLdouble red, GLdouble green, GLdouble blue);
    void glColor3bv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_BYTE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glColor3bv(reinterpret_cast<const GLbyte *>(array));
%End

    void glColor3b(GLbyte red, GLbyte green, GLbyte blue);
    void glBitmap(GLsizei width, GLsizei height, GLfloat xorig, GLfloat yorig, GLfloat xmove, GLfloat ymove, SIP_PYOBJECT bitmap /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a6, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glBitmap(a0, a1, a2, a3, a4, a5,
                    reinterpret_cast<const GLubyte *>(array));
%End

    void glBegin(GLenum mode);
    void glListBase(GLuint base);
    GLuint glGenLists(GLsizei range);
    void glDeleteLists(GLuint list, GLsizei range);
    void glCallList(GLuint list);
    void glEndList();
    void glNewList(GLuint list, GLenum mode);
    void glPushClientAttrib(GLbitfield mask);
    void glPopClientAttrib();
    void glVertexPointer(GLint size, GLenum type, GLsizei stride, SIP_PYOBJECT pointer /TypeHint="PYQT_OPENGL_BOUND_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array_cached(&sipError, a3, a1, sipSelf,
                "VertexPointer", 0);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexPointer(a0, a1, a2, array);
%End

    void glTexCoordPointer(GLint size, GLenum type, GLsizei stride, SIP_PYOBJECT pointer /TypeHint="PYQT_OPENGL_BOUND_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array_cached(&sipError, a3, a1, sipSelf,
                "TexCoordPointer", 0);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexCoordPointer(a0, a1, a2, array);
%End

    void glNormalPointer(GLenum type, GLsizei stride, SIP_PYOBJECT pointer /TypeHint="PYQT_OPENGL_BOUND_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array_cached(&sipError, a2, a0, sipSelf,
                "NormalPointer", 0);
        
        if (sipError == sipErrorNone)
            sipCpp->glNormalPointer(a0, a1, array);
%End

    void glIndexPointer(GLenum type, GLsizei stride, SIP_PYOBJECT pointer /TypeHint="PYQT_OPENGL_BOUND_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array_cached(&sipError, a2, a0, sipSelf,
                "IndexPointer", 0);
        
        if (sipError == sipErrorNone)
            sipCpp->glIndexPointer(a0, a1, array);
%End

    void glEnableClientState(GLenum array);
    void glEdgeFlagPointer(GLsizei stride, SIP_PYOBJECT pointer /TypeHint="PYQT_OPENGL_BOUND_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array_cached(&sipError, a1,
                GL_UNSIGNED_BYTE, sipSelf, "EdgeFlagPointer", 0);
        
        if (sipError == sipErrorNone)
            sipCpp->glEdgeFlagPointer(a0, array);
%End

    void glDisableClientState(GLenum array);
    void glColorPointer(GLint size, GLenum type, GLsizei stride, SIP_PYOBJECT pointer /TypeHint="PYQT_OPENGL_BOUND_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array_cached(&sipError, a3, a1, sipSelf,
                "ColorPointer", 0);
        
        if (sipError == sipErrorNone)
            sipCpp->glColorPointer(a0, a1, a2, array);
%End

    void glArrayElement(GLint i);
    void glResetMinmax(GLenum target);
    void glResetHistogram(GLenum target);
    void glMinmax(GLenum target, GLenum internalformat, GLboolean sink);
    void glHistogram(GLenum target, GLsizei width, GLenum internalformat, GLboolean sink);
    void glGetConvolutionParameteriv(GLenum target, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[int, Tuple[int, int, int, int]]"/);
%MethodCode
        GLint params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_CONVOLUTION_BORDER_COLOR:
        case GL_CONVOLUTION_FILTER_SCALE:
        case GL_CONVOLUTION_FILTER_BIAS:
            nr_params = 4;
            break;
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetConvolutionParameteriv(a0, a1, params);
        
        a2 = qpyopengl_from_GLint(&sipIsErr, params, nr_params);
%End

    void glGetConvolutionParameterfv(GLenum target, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[float, Tuple[float, float, float, float]]"/);
%MethodCode
        GLfloat params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_CONVOLUTION_BORDER_COLOR:
        case GL_CONVOLUTION_FILTER_SCALE:
        case GL_CONVOLUTION_FILTER_BIAS:
            nr_params = 4;
            break;
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetConvolutionParameterfv(a0, a1, params);
        
        a2 = qpyopengl_from_GLfloat(&sipIsErr, params, nr_params);
%End

    void glCopyConvolutionFilter2D(GLenum target, GLenum internalformat, GLint x, GLint y, GLsizei width, GLsizei height);
    void glCopyConvolutionFilter1D(GLenum target, GLenum internalformat, GLint x, GLint y, GLsizei width);
    void glConvolutionParameteriv(GLenum target, GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glConvolutionParameteriv(a0, a1,
                    reinterpret_cast<const GLint *>(array));
%End

    void glConvolutionParameteri(GLenum target, GLenum pname, GLint params);
    void glConvolutionParameterfv(GLenum target, GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glConvolutionParameterfv(a0, a1,
                    reinterpret_cast<const GLfloat *>(array));
%End

    void glConvolutionParameterf(GLenum target, GLenum pname, GLfloat params);
    void glConvolutionFilter2D(GLenum target, GLenum internalformat, GLsizei width, GLsizei height, GLenum format, GLenum type, SIP_PYOBJECT image /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a6, a5, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glConvolutionFilter2D(a0, a1, a2, a3, a4, a5, array);
%End

    void glConvolutionFilter1D(GLenum target, GLenum internalformat, GLsizei width, GLenum format, GLenum type, SIP_PYOBJECT image /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a5, a4, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glConvolutionFilter1D(a0, a1, a2, a3, a4, array);
%End

    void glCopyColorSubTable(GLenum target, GLsizei start, GLint x, GLint y, GLsizei width);
    void glColorSubTable(GLenum target, GLsizei start, GLsizei count, GLenum format, GLenum type, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a5, a4, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glColorSubTable(a0, a1, a2, a3, a4, array);
%End

    void glGetColorTableParameteriv(GLenum target, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[int, Tuple[int, int, int, int]]"/);
%MethodCode
        GLint params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_COLOR_TABLE:
        case GL_POST_CONVOLUTION_COLOR_TABLE:
        case GL_POST_COLOR_MATRIX_COLOR_TABLE:
            nr_params = 4;
            break;
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetColorTableParameteriv(a0, a1, params);
        
        a2 = qpyopengl_from_GLint(&sipIsErr, params, nr_params);
%End

    void glGetColorTableParameterfv(GLenum target, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[float, Tuple[float, float, float, float]]"/);
%MethodCode
        GLfloat params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_COLOR_TABLE:
        case GL_POST_CONVOLUTION_COLOR_TABLE:
        case GL_POST_COLOR_MATRIX_COLOR_TABLE:
            nr_params = 4;
            break;
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetColorTableParameterfv(a0, a1, params);
        
        a2 = qpyopengl_from_GLfloat(&sipIsErr, params, nr_params);
%End

    void glCopyColorTable(GLenum target, GLenum internalformat, GLint x, GLint y, GLsizei width);
    void glColorTableParameteriv(GLenum target, GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glColorTableParameteriv(a0, a1,
                    reinterpret_cast<const GLint *>(array));
%End

    void glColorTableParameterfv(GLenum target, GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glColorTableParameterfv(a0, a1,
                    reinterpret_cast<const GLfloat *>(array));
%End

    void glColorTable(GLenum target, GLenum internalformat, GLsizei width, GLenum format, GLenum type, SIP_PYOBJECT table /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a5, a4, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glColorTable(a0, a1, a2, a3, a4, array);
%End

    void glMultTransposeMatrixd(SIP_PYOBJECT m /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMultTransposeMatrixd(reinterpret_cast<const GLdouble *>(array));
%End

    void glMultTransposeMatrixf(SIP_PYOBJECT m /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMultTransposeMatrixf(reinterpret_cast<const GLfloat *>(array));
%End

    void glLoadTransposeMatrixd(SIP_PYOBJECT m /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glLoadTransposeMatrixd(reinterpret_cast<const GLdouble *>(array));
%End

    void glLoadTransposeMatrixf(SIP_PYOBJECT m /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glLoadTransposeMatrixf(reinterpret_cast<const GLfloat *>(array));
%End

    void glMultiTexCoord4sv(GLenum target, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMultiTexCoord4sv(a0, reinterpret_cast<const GLshort *>(array));
%End

    void glMultiTexCoord4s(GLenum target, GLshort s, GLshort t, GLshort r, GLshort q);
    void glMultiTexCoord4iv(GLenum target, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMultiTexCoord4iv(a0, reinterpret_cast<const GLint *>(array));
%End

    void glMultiTexCoord4i(GLenum target, GLint s, GLint t, GLint r, GLint q);
    void glMultiTexCoord4fv(GLenum target, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMultiTexCoord4fv(a0, reinterpret_cast<const GLfloat *>(array));
%End

    void glMultiTexCoord4f(GLenum target, GLfloat s, GLfloat t, GLfloat r, GLfloat q);
    void glMultiTexCoord4dv(GLenum target, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMultiTexCoord4dv(a0, reinterpret_cast<const GLdouble *>(array));
%End

    void glMultiTexCoord4d(GLenum target, GLdouble s, GLdouble t, GLdouble r, GLdouble q);
    void glMultiTexCoord3sv(GLenum target, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMultiTexCoord3sv(a0, reinterpret_cast<const GLshort *>(array));
%End

    void glMultiTexCoord3s(GLenum target, GLshort s, GLshort t, GLshort r);
    void glMultiTexCoord3iv(GLenum target, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMultiTexCoord3iv(a0, reinterpret_cast<const GLint *>(array));
%End

    void glMultiTexCoord3i(GLenum target, GLint s, GLint t, GLint r);
    void glMultiTexCoord3fv(GLenum target, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMultiTexCoord3fv(a0, reinterpret_cast<const GLfloat *>(array));
%End

    void glMultiTexCoord3f(GLenum target, GLfloat s, GLfloat t, GLfloat r);
    void glMultiTexCoord3dv(GLenum target, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMultiTexCoord3dv(a0, reinterpret_cast<const GLdouble *>(array));
%End

    void glMultiTexCoord3d(GLenum target, GLdouble s, GLdouble t, GLdouble r);
    void glMultiTexCoord2sv(GLenum target, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMultiTexCoord2sv(a0, reinterpret_cast<const GLshort *>(array));
%End

    void glMultiTexCoord2s(GLenum target, GLshort s, GLshort t);
    void glMultiTexCoord2iv(GLenum target, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMultiTexCoord2iv(a0, reinterpret_cast<const GLint *>(array));
%End

    void glMultiTexCoord2i(GLenum target, GLint s, GLint t);
    void glMultiTexCoord2fv(GLenum target, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMultiTexCoord2fv(a0, reinterpret_cast<const GLfloat *>(array));
%End

    void glMultiTexCoord2f(GLenum target, GLfloat s, GLfloat t);
    void glMultiTexCoord2dv(GLenum target, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMultiTexCoord2dv(a0, reinterpret_cast<const GLdouble *>(array));
%End

    void glMultiTexCoord2d(GLenum target, GLdouble s, GLdouble t);
    void glMultiTexCoord1sv(GLenum target, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMultiTexCoord1sv(a0, reinterpret_cast<const GLshort *>(array));
%End

    void glMultiTexCoord1s(GLenum target, GLshort s);
    void glMultiTexCoord1iv(GLenum target, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMultiTexCoord1iv(a0, reinterpret_cast<const GLint *>(array));
%End

    void glMultiTexCoord1i(GLenum target, GLint s);
    void glMultiTexCoord1fv(GLenum target, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMultiTexCoord1fv(a0, reinterpret_cast<const GLfloat *>(array));
%End

    void glMultiTexCoord1f(GLenum target, GLfloat s);
    void glMultiTexCoord1dv(GLenum target, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glMultiTexCoord1dv(a0, reinterpret_cast<const GLdouble *>(array));
%End

    void glMultiTexCoord1d(GLenum target, GLdouble s);
    void glClientActiveTexture(GLenum texture);
    void glWindowPos3sv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glWindowPos3sv(reinterpret_cast<const GLshort *>(array));
%End

    void glWindowPos3s(GLshort x, GLshort y, GLshort z);
    void glWindowPos3iv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glWindowPos3iv(reinterpret_cast<const GLint *>(array));
%End

    void glWindowPos3i(GLint x, GLint y, GLint z);
    void glWindowPos3fv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glWindowPos3fv(reinterpret_cast<const GLfloat *>(array));
%End

    void glWindowPos3f(GLfloat x, GLfloat y, GLfloat z);
    void glWindowPos3dv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glWindowPos3dv(reinterpret_cast<const GLdouble *>(array));
%End

    void glWindowPos3d(GLdouble x, GLdouble y, GLdouble z);
    void glWindowPos2sv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glWindowPos2sv(reinterpret_cast<const GLshort *>(array));
%End

    void glWindowPos2s(GLshort x, GLshort y);
    void glWindowPos2iv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glWindowPos2iv(reinterpret_cast<const GLint *>(array));
%End

    void glWindowPos2i(GLint x, GLint y);
    void glWindowPos2fv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glWindowPos2fv(reinterpret_cast<const GLfloat *>(array));
%End

    void glWindowPos2f(GLfloat x, GLfloat y);
    void glWindowPos2dv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glWindowPos2dv(reinterpret_cast<const GLdouble *>(array));
%End

    void glWindowPos2d(GLdouble x, GLdouble y);
    void glSecondaryColorPointer(GLint size, GLenum type, GLsizei stride, SIP_PYOBJECT pointer /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a3, a1, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glSecondaryColorPointer(a0, a1, a2, array);
%End

    void glSecondaryColor3usv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_UNSIGNED_SHORT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glSecondaryColor3usv(reinterpret_cast<const GLushort *>(array));
%End

    void glSecondaryColor3us(GLushort red, GLushort green, GLushort blue);
    void glSecondaryColor3uiv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_UNSIGNED_INT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glSecondaryColor3uiv(reinterpret_cast<const GLuint *>(array));
%End

    void glSecondaryColor3ui(GLuint red, GLuint green, GLuint blue);
    void glSecondaryColor3ubv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glSecondaryColor3ubv(reinterpret_cast<const GLubyte *>(array));
%End

    void glSecondaryColor3ub(GLubyte red, GLubyte green, GLubyte blue);
    void glSecondaryColor3sv(SIP_PYBUFFER v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glSecondaryColor3sv(reinterpret_cast<const GLshort *>(array));
%End

    void glSecondaryColor3s(GLshort red, GLshort green, GLshort blue);
    void glSecondaryColor3iv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glSecondaryColor3iv(reinterpret_cast<const GLint *>(array));
%End

    void glSecondaryColor3i(GLint red, GLint green, GLint blue);
    void glSecondaryColor3fv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glSecondaryColor3fv(reinterpret_cast<const GLfloat *>(array));
%End

    void glSecondaryColor3f(GLfloat red, GLfloat green, GLfloat blue);
    void glSecondaryColor3dv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glSecondaryColor3dv(reinterpret_cast<const GLdouble *>(array));
%End

    void glSecondaryColor3d(GLdouble red, GLdouble green, GLdouble blue);
    void glSecondaryColor3bv(SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_BYTE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glSecondaryColor3bv(reinterpret_cast<const GLbyte *>(array));
%End

    void glSecondaryColor3b(GLbyte red, GLbyte green, GLbyte blue);
    void glFogCoordPointer(GLenum type, GLsizei stride, SIP_PYOBJECT pointer /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, a0, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glFogCoordPointer(a0, a1, array);
%End

    void glFogCoorddv(SIP_PYOBJECT coord /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glFogCoorddv(reinterpret_cast<const GLdouble *>(array));
%End

    void glFogCoordd(GLdouble coord);
    void glFogCoordfv(SIP_PYOBJECT coord /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a0, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glFogCoordfv(reinterpret_cast<const GLfloat *>(array));
%End

    void glFogCoordf(GLfloat coord);
    void glVertexAttrib4usv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_UNSIGNED_SHORT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib4usv(a0, reinterpret_cast<const GLushort *>(array));
%End

    void glVertexAttrib4uiv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_UNSIGNED_INT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib4uiv(a0, reinterpret_cast<const GLuint *>(array));
%End

    void glVertexAttrib4ubv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib4ubv(a0, reinterpret_cast<const GLubyte *>(array));
%End

    void glVertexAttrib4sv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib4sv(a0, reinterpret_cast<const GLshort *>(array));
%End

    void glVertexAttrib4s(GLuint index, GLshort x, GLshort y, GLshort z, GLshort w);
    void glVertexAttrib4iv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib4iv(a0, reinterpret_cast<const GLint *>(array));
%End

    void glVertexAttrib4fv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib4fv(a0, reinterpret_cast<const GLfloat *>(array));
%End

    void glVertexAttrib4f(GLuint index, GLfloat x, GLfloat y, GLfloat z, GLfloat w);
    void glVertexAttrib4dv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib4dv(a0, reinterpret_cast<const GLdouble *>(array));
%End

    void glVertexAttrib4d(GLuint index, GLdouble x, GLdouble y, GLdouble z, GLdouble w);
    void glVertexAttrib4bv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_BYTE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib4bv(a0, reinterpret_cast<const GLbyte *>(array));
%End

    void glVertexAttrib4Nusv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_UNSIGNED_SHORT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib4Nusv(a0, reinterpret_cast<const GLushort *>(array));
%End

    void glVertexAttrib4Nuiv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_UNSIGNED_INT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib4Nuiv(a0, reinterpret_cast<const GLuint *>(array));
%End

    void glVertexAttrib4Nubv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib4Nubv(a0, reinterpret_cast<const GLubyte *>(array));
%End

    void glVertexAttrib4Nub(GLuint index, GLubyte x, GLubyte y, GLubyte z, GLubyte w);
    void glVertexAttrib4Nsv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib4Nsv(a0, reinterpret_cast<const GLshort *>(array));
%End

    void glVertexAttrib4Niv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib4Niv(a0, reinterpret_cast<const GLint *>(array));
%End

    void glVertexAttrib4Nbv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_BYTE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib4Nbv(a0, reinterpret_cast<const GLbyte *>(array));
%End

    void glVertexAttrib3sv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib3sv(a0, reinterpret_cast<const GLshort *>(array));
%End

    void glVertexAttrib3s(GLuint index, GLshort x, GLshort y, GLshort z);
    void glVertexAttrib3fv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib3fv(a0, reinterpret_cast<const GLfloat *>(array));
%End

    void glVertexAttrib3f(GLuint index, GLfloat x, GLfloat y, GLfloat z);
    void glVertexAttrib3dv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib3dv(a0, reinterpret_cast<const GLdouble *>(array));
%End

    void glVertexAttrib3d(GLuint index, GLdouble x, GLdouble y, GLdouble z);
    void glVertexAttrib2sv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib2sv(a0, reinterpret_cast<const GLshort *>(array));
%End

    void glVertexAttrib2s(GLuint index, GLshort x, GLshort y);
    void glVertexAttrib2fv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib2fv(a0, reinterpret_cast<const GLfloat *>(array));
%End

    void glVertexAttrib2f(GLuint index, GLfloat x, GLfloat y);
    void glVertexAttrib2dv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib2dv(a0, reinterpret_cast<const GLdouble *>(array));
%End

    void glVertexAttrib2d(GLuint index, GLdouble x, GLdouble y);
    void glVertexAttrib1sv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_SHORT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib1sv(a0, reinterpret_cast<const GLshort *>(array));
%End

    void glVertexAttrib1s(GLuint index, GLshort x);
    void glVertexAttrib1fv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib1fv(a0, reinterpret_cast<const GLfloat *>(array));
%End

    void glVertexAttrib1f(GLuint index, GLfloat x);
    void glVertexAttrib1dv(GLuint index, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_DOUBLE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib1dv(a0, reinterpret_cast<const GLdouble *>(array));
%End

    void glVertexAttrib1d(GLuint index, GLdouble x);
};

%End
