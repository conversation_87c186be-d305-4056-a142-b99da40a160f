// qopenglfunctions_es2.sip generated by MetaSIP
//
// This file is part of the QtOpenGL Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_OpenGL_ES2)

class QOpenGLFunctions_ES2 : public QAbstractOpenGLFunctions
{
%TypeHeaderCode
#include <qopenglfunctions_es2.h>
%End

public:
    QOpenGLFunctions_ES2();
    bool initializeOpenGLFunctions();
    void glActiveTexture(GLenum texture);
    void glAttachShader(GLuint program, GLuint shader);
    void glBindAttribLocation(GLuint program, GLuint index, const GLchar *name);
    void glBindBuffer(GLenum target, GLuint buffer);
    void glBindFramebuffer(GLenum target, GLuint framebuffer);
    void glBindRenderbuffer(GLenum target, GLuint renderbuffer);
    void glBindTexture(GLenum target, GLuint texture);
    void glBlendColor(GLclampf red, GLclampf green, GLclampf blue, GLclampf alpha);
    void glBlendEquation(GLenum mode);
    void glBlendEquationSeparate(GLenum modeRGB, GLenum modeAlpha);
    void glBlendFunc(GLenum sfactor, GLenum dfactor);
    void glBlendFuncSeparate(GLenum srcRGB, GLenum dstRGB, GLenum srcAlpha, GLenum dstAlpha);
    void glBufferData(GLenum target, GLsizeiptr size, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/, GLenum usage);
%MethodCode
        const GLvoid *array;
        
        if (a2 == Py_None)
            array = 0;
        else
            array = qpyopengl_value_array(&sipError, a2, GL_UNSIGNED_BYTE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glBufferData(a0, a1, array, a3);
%End

    void glBufferSubData(GLenum target, GLintptr offset, GLsizeiptr size, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array;
        
        if (a3 == Py_None)
            array = 0;
        else
            array = qpyopengl_value_array(&sipError, a3, GL_UNSIGNED_BYTE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glBufferSubData(a0, a1, a2, array);
%End

    GLenum glCheckFramebufferStatus(GLenum target);
    void glClear(GLbitfield mask);
    void glClearColor(GLclampf red, GLclampf green, GLclampf blue, GLclampf alpha);
    void glClearDepthf(GLclampf depth);
    void glClearStencil(GLint s);
    void glColorMask(GLboolean red, GLboolean green, GLboolean blue, GLboolean alpha);
    void glCompileShader(GLuint shader);
    void glCompressedTexImage2D(GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLint border, GLsizei imageSize, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a7, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glCompressedTexImage2D(a0, a1, a2, a3, a4, a5, a6, array);
%End

    void glCompressedTexSubImage2D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLsizei imageSize, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a8, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glCompressedTexSubImage2D(a0, a1, a2, a3, a4, a5, a6, a7, array);
%End

    void glCopyTexImage2D(GLenum target, GLint level, GLenum internalformat, GLint x, GLint y, GLsizei width, GLsizei height, GLint border);
    void glCopyTexSubImage2D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint x, GLint y, GLsizei width, GLsizei height);
    GLuint glCreateProgram();
    GLuint glCreateShader(GLenum type);
    void glCullFace(GLenum mode);
    void glDeleteBuffers(GLsizei n, SIP_PYOBJECT buffers /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_UNSIGNED_INT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glDeleteBuffers(a0, reinterpret_cast<const GLuint *>(array));
%End

    void glDeleteFramebuffers(GLsizei n, SIP_PYOBJECT framebuffers);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_UNSIGNED_INT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glDeleteFramebuffers(a0, reinterpret_cast<const GLuint *>(array));
%End

    void glDeleteProgram(GLuint program);
    void glDeleteRenderbuffers(GLsizei n, SIP_PYOBJECT renderbuffers);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_UNSIGNED_INT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glDeleteRenderbuffers(a0, reinterpret_cast<const GLuint *>(array));
%End

    void glDeleteShader(GLuint shader);
    void glDeleteTextures(GLsizei n, SIP_PYOBJECT textures /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_UNSIGNED_INT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glDeleteTextures(a0, reinterpret_cast<const GLuint *>(array));
%End

    void glDepthFunc(GLenum func);
    void glDepthMask(GLboolean flag);
    void glDepthRangef(GLclampf zNear, GLclampf zFar);
    void glDetachShader(GLuint program, GLuint shader);
    void glDisable(GLenum cap);
    void glDisableVertexAttribArray(GLuint index);
    void glDrawArrays(GLenum mode, GLint first, GLsizei count);
    void glDrawElements(GLenum mode, GLsizei count, GLenum type, SIP_PYOBJECT indices /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a3, a2, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glDrawElements(a0, a1, a2, array);
%End

    void glEnable(GLenum cap);
    void glEnableVertexAttribArray(GLuint index);
    void glFinish();
    void glFlush();
    void glFramebufferRenderbuffer(GLenum target, GLenum attachment, GLenum renderbuffertarget, GLuint renderbuffer);
    void glFramebufferTexture2D(GLenum target, GLenum attachment, GLenum textarget, GLuint texture, GLint level);
    void glFrontFace(GLenum mode);
    void glGenBuffers(GLsizei n, SIP_PYOBJECT *buffers /TypeHint="Union[int, Tuple[int, ...]]"/);
%MethodCode
        GLuint *params = new GLuint[a0];
            
        sipCpp->glGenBuffers(a0, params);
        
        a1 = qpyopengl_from_GLuint(&sipIsErr, params, a0);
        
        delete[] params;
%End

    void glGenerateMipmap(GLenum target);
    void glGenFramebuffers(GLsizei n, SIP_PYOBJECT framebuffers /TypeHint="Union[int, Tuple[int, ...]]"/);
%MethodCode
        GLuint *params = new GLuint[a0];
            
        sipCpp->glGenFramebuffers(a0, params);
        
        a1 = qpyopengl_from_GLuint(&sipIsErr, params, a0);
        
        delete[] params;
%End

    void glGenRenderbuffers(GLsizei n, SIP_PYOBJECT *renderbuffers /TypeHint="Union[int, Tuple[int, ...]]"/);
%MethodCode
        GLuint *params = new GLuint[a0];
            
        sipCpp->glGenRenderbuffers(a0, params);
        
        a1 = qpyopengl_from_GLuint(&sipIsErr, params, a0);
        
        delete[] params;
%End

    void glGenTextures(GLsizei n, SIP_PYOBJECT textures /TypeHint="Union[int, Tuple[int, ...]]"/);
%MethodCode
        GLuint *params = new GLuint[a0];
            
        sipCpp->glGenTextures(a0, params);
        
        a1 = qpyopengl_from_GLuint(&sipIsErr, params, a0);
        
        delete[] params;
%End

    SIP_PYOBJECT glGetActiveAttrib(GLuint program, GLuint index) /TypeHint="Tuple[str, int, int]"/;
%MethodCode
        GLint bufsize;
        
        sipCpp->glGetProgramiv(a0, GL_ACTIVE_ATTRIBUTE_MAX_LENGTH, &bufsize);
        
        GLchar *name = new GLchar[bufsize];
        GLint size;
        GLenum type;
        
        sipCpp->glGetActiveAttrib(a0, a1, bufsize, 0, &size, &type, name);
        
        sipRes = Py_BuildValue("siI", name, size, type);
        
        if (!sipRes)
            sipIsErr = 1;
        
        delete[] name;
%End

    SIP_PYOBJECT glGetActiveUniform(GLuint program, GLuint index) /TypeHint="Tuple[str, int, int]"/;
%MethodCode
        GLint bufsize;
        
        sipCpp->glGetProgramiv(a0, GL_ACTIVE_UNIFORM_MAX_LENGTH, &bufsize);
        
        GLchar *name = new GLchar[bufsize];
        GLint size;
        GLenum type;
        
        sipCpp->glGetActiveUniform(a0, a1, bufsize, 0, &size, &type, name);
        
        sipRes = Py_BuildValue("siI", name, size, type);
        
        if (!sipRes)
            sipIsErr = 1;
        
        delete[] name;
%End

    SIP_PYOBJECT glGetAttachedShaders(GLuint program) /TypeHint="Tuple[int, ...]"/;
%MethodCode
        GLint nr_shaders;
        
        sipCpp->glGetProgramiv(a0, GL_ATTACHED_SHADERS, &nr_shaders);
        
        if (nr_shaders < 1)
        {
            sipRes = PyTuple_New(0);
        }
        else
        {
            GLuint *shaders = new GLuint[nr_shaders];
        
            sipCpp->glGetAttachedShaders(a0, nr_shaders, 0, shaders);
        
            sipRes = PyTuple_New(nr_shaders);
        
            if (sipRes)
            {
                for (GLint i = 0; i < nr_shaders; ++i)
                {
                    PyObject *itm = PyLong_FromLong(shaders[i]);
                    
                    if (!itm)
                    {
                        Py_DECREF(sipRes);
                        sipRes = 0;
                        break;
                    }
                    
                    PyTuple_SetItem(sipRes, i, itm);
                }
            }
        
            delete[] shaders;
        }
        
        if (!sipRes)
            sipIsErr = 1;
%End

    int glGetAttribLocation(GLuint program, const GLchar *name);
    void glGetBooleanv(GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[bool, Tuple[bool, ...]]"/);
%MethodCode
        GLboolean fixed_params[16], *params;
        GLint nr_params;
        GLenum query;
        
        nr_params = qpyopengl_get(a0, &query);
        
        if (nr_params == 0)
        {
            sipCpp->glGetIntegerv(query, &nr_params);
            params = new GLboolean[nr_params];
        }
        else
        {
            params = fixed_params;
        }
        
        sipCpp->glGetBooleanv(a0, params);
        a1 = qpyopengl_from_GLboolean(&sipIsErr, params, nr_params);
            
        if (params != fixed_params)
            delete[] params;
%End

    void glGetBufferParameteriv(GLenum target, GLenum pname, GLint *params);
    GLenum glGetError();
    void glGetFloatv(GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[float, Tuple[float, ...]]"/);
%MethodCode
        GLfloat fixed_params[16], *params;
        GLint nr_params;
        GLenum query;
        
        nr_params = qpyopengl_get(a0, &query);
        
        if (nr_params == 0)
        {
            sipCpp->glGetIntegerv(query, &nr_params);
            params = new GLfloat[nr_params];
        }
        else
        {
            params = fixed_params;
        }
        
        sipCpp->glGetFloatv(a0, params);
        a1 = qpyopengl_from_GLfloat(&sipIsErr, params, nr_params);
            
        if (params != fixed_params)
            delete[] params;
%End

    void glGetFramebufferAttachmentParameteriv(GLenum target, GLenum attachment, GLenum pname, GLint *params);
    void glGetIntegerv(GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[int, Tuple[int, ...]]"/);
%MethodCode
        GLint fixed_params[16], *params;
        GLint nr_params;
        GLenum query;
        
        nr_params = qpyopengl_get(a0, &query);
        
        if (nr_params == 0)
        {
            sipCpp->glGetIntegerv(query, &nr_params);
            params = new GLint[nr_params];
        }
        else
        {
            params = fixed_params;
        }
        
        sipCpp->glGetIntegerv(a0, params);
        a1 = qpyopengl_from_GLint(&sipIsErr, params, nr_params);
            
        if (params != fixed_params)
            delete[] params;
%End

    void glGetProgramiv(GLuint program, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[int, Tuple[int, int, int]]"/);
%MethodCode
        GLint params[3];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        #if defined(GL_COMPUTE_LOCAL_WORK_SIZE)
        case GL_COMPUTE_LOCAL_WORK_SIZE:
            nr_params = 3;
            break;
        #endif
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetProgramiv(a0, a1, params);
        
        a2 = qpyopengl_from_GLint(&sipIsErr, params, nr_params);
%End

    SIP_PYOBJECT glGetProgramInfoLog(GLuint program) /TypeHint="bytes"/;
%MethodCode
        GLint bufsize;
        
        sipCpp->glGetProgramiv(a0, GL_INFO_LOG_LENGTH, &bufsize);
        
        if (bufsize > 0)
        {
            GLchar *log = new GLchar[bufsize];
        
            sipCpp->glGetProgramInfoLog(a0, bufsize, 0, log);
            sipRes = PyBytes_FromString(log);
        
            delete[] log;
        }
        else
        {
            sipRes = PyBytes_FromString("");
        }
%End

    void glGetRenderbufferParameteriv(GLenum target, GLenum pname, GLint *params);
    void glGetShaderiv(GLuint shader, GLenum pname, GLint *params);
    SIP_PYOBJECT glGetShaderInfoLog(GLuint shader) /TypeHint="bytes"/;
%MethodCode
        GLint bufsize;
        
        sipCpp->glGetShaderiv(a0, GL_INFO_LOG_LENGTH, &bufsize);
        
        if (bufsize > 0)
        {
            GLchar *log = new GLchar[bufsize];
        
            sipCpp->glGetShaderInfoLog(a0, bufsize, 0, log);
            sipRes = PyBytes_FromString(log);
        
            delete[] log;
        }
        else
        {
            sipRes = PyBytes_FromString("");
        }
%End

    SIP_PYOBJECT glGetShaderPrecisionFormat(GLenum shadertype, GLenum precisiontype) /TypeHint="Tuple[Tuple[int, int], int]"/;
%MethodCode
        GLint range[2], precision;
        
        sipCpp->glGetShaderPrecisionFormat(a0, a1, range, &precision);
        
        sipRes = Py_BuildValue("(ii)i", (int)range[0], (int)range[1], (int)precision);
        
        if (!sipRes)
            sipIsErr = 1;
%End

    SIP_PYOBJECT glGetShaderSource(GLuint shader) /TypeHint="bytes"/;
%MethodCode
        GLint bufsize;
        
        sipCpp->glGetShaderiv(a0, GL_SHADER_SOURCE_LENGTH, &bufsize);
        
        if (bufsize > 0)
        {
            GLchar *source = new GLchar[bufsize];
        
            sipCpp->glGetShaderSource(a0, bufsize, 0, source);
            sipRes = PyBytes_FromString(source);
        
            delete[] source;
        }
        else
        {
            sipRes = PyBytes_FromString("");
        }
%End

    const char *glGetString(GLenum name);
%MethodCode
        sipRes = reinterpret_cast<const char *>(sipCpp->glGetString(a0));
%End

    void glGetTexParameterfv(GLenum target, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[float, Tuple[float, float, float, float]]"/);
%MethodCode
        GLfloat params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        #if defined(GL_TEXTURE_SWIZZLE_RGBA) || defined(GL_TEXTURE_BORDER_COLOR)
        #if defined(GL_TEXTURE_SWIZZLE_RGBA)
        case GL_TEXTURE_SWIZZLE_RGBA:
        #endif
        #if defined(GL_TEXTURE_BORDER_COLOR)
        case GL_TEXTURE_BORDER_COLOR:
        #endif
            nr_params = 4;
            break;
        #endif
            
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetTexParameterfv(a0, a1, params);
        
        a2 = qpyopengl_from_GLfloat(&sipIsErr, params, nr_params);
%End

    void glGetTexParameteriv(GLenum target, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[int, Tuple[int, int, int, int]]"/);
%MethodCode
        GLint params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        #if defined(GL_TEXTURE_SWIZZLE_RGBA) || defined(GL_TEXTURE_BORDER_COLOR)
        #if defined(GL_TEXTURE_SWIZZLE_RGBA)
        case GL_TEXTURE_SWIZZLE_RGBA:
        #endif
        #if defined(GL_TEXTURE_BORDER_COLOR)
        case GL_TEXTURE_BORDER_COLOR:
        #endif
            nr_params = 4;
            break;
        #endif
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetTexParameteriv(a0, a1, params);
        
        a2 = qpyopengl_from_GLint(&sipIsErr, params, nr_params);
%End

    int glGetUniformLocation(GLuint program, const GLchar *name);
    void glGetVertexAttribfv(GLuint index, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[float, Tuple[float, float, float, float]]"/);
%MethodCode
        GLfloat params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_CURRENT_VERTEX_ATTRIB:
            nr_params = 4;
            break;
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetVertexAttribfv(a0, a1, params);
        
        a2 = qpyopengl_from_GLfloat(&sipIsErr, params, nr_params);
%End

    void glGetVertexAttribiv(GLuint index, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[int, Tuple[int, int, int, int]]"/);
%MethodCode
        GLint params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_CURRENT_VERTEX_ATTRIB:
            nr_params = 4;
            break;
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetVertexAttribiv(a0, a1, params);
        
        a2 = qpyopengl_from_GLint(&sipIsErr, params, nr_params);
%End

    void glHint(GLenum target, GLenum mode);
    GLboolean glIsBuffer(GLuint buffer);
    GLboolean glIsEnabled(GLenum cap);
    GLboolean glIsFramebuffer(GLuint framebuffer);
    GLboolean glIsProgram(GLuint program);
    GLboolean glIsRenderbuffer(GLuint renderbuffer);
    GLboolean glIsShader(GLuint shader);
    GLboolean glIsTexture(GLuint texture);
    void glLineWidth(GLfloat width);
    void glLinkProgram(GLuint program);
    void glPixelStorei(GLenum pname, GLint param);
    void glPolygonOffset(GLfloat factor, GLfloat units);
    SIP_PYOBJECT glReadPixels(GLint x, GLint y, GLsizei width, GLsizei height, GLenum format, GLenum type) /TypeHint="Union[Tuple[float, ...], Tuple[int, ...]]"/;
%MethodCode
        int components;
        
        switch (a4)
        {
            case GL_RGB:
            {
                components = 3;
                break;
            }
        
            case GL_RGBA:
            {
                components = 4;
                break;
            }
        
            case GL_ALPHA:
            {
                components = 1;
                break;
            }
        
            default:
                components = 0;
        }
        
        Py_ssize_t length = components * a2 * a3;
        
        switch (a5)
        {
            // TODO: Implement array convertors for these formats.
            case GL_UNSIGNED_SHORT_5_6_5:
            case GL_UNSIGNED_SHORT_4_4_4_4:
            case GL_UNSIGNED_SHORT_5_5_5_1:
            case GL_UNSIGNED_BYTE:
            default:
                sipIsErr = 1;
                PyErr_SetString(PyExc_ValueError, "pixel data format not supported");
        }
%End

    void glReleaseShaderCompiler();
    void glRenderbufferStorage(GLenum target, GLenum internalformat, GLsizei width, GLsizei height);
    void glSampleCoverage(GLclampf value, GLboolean invert);
    void glScissor(GLint x, GLint y, GLsizei width, GLsizei height);
    void glStencilFunc(GLenum func, GLint ref, GLuint mask);
    void glStencilFuncSeparate(GLenum face, GLenum func, GLint ref, GLuint mask);
    void glStencilMask(GLuint mask);
    void glStencilMaskSeparate(GLenum face, GLuint mask);
    void glStencilOp(GLenum fail, GLenum zfail, GLenum zpass);
    void glStencilOpSeparate(GLenum face, GLenum fail, GLenum zfail, GLenum zpass);
    void glTexImage2D(GLenum target, GLint level, GLint internalformat, GLsizei width, GLsizei height, GLint border, GLenum format, GLenum type, SIP_PYOBJECT pixels /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a8, a7, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexImage2D(a0, a1, a2, a3, a4, a5, a6, a7, array);
%End

    void glTexParameterf(GLenum target, GLenum pname, GLfloat param);
    void glTexParameterfv(GLenum target, GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexParameterfv(a0, a1, reinterpret_cast<const GLfloat *>(array));
%End

    void glTexParameteri(GLenum target, GLenum pname, GLint param);
    void glTexParameteriv(GLenum target, GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexParameteriv(a0, a1, reinterpret_cast<const GLint *>(array));
%End

    void glTexSubImage2D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLenum type, SIP_PYOBJECT pixels /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a8, a7, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexSubImage2D(a0, a1, a2, a3, a4, a5, a6, a7, array);
%End

    void glUniform1f(GLint location, GLfloat x);
    void glUniform1fv(GLint location, GLsizei count, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform1fv(a0, a1, reinterpret_cast<const GLfloat *>(array));
%End

    void glUniform1i(GLint location, GLint x);
    void glUniform1iv(GLint location, GLsizei count, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform1iv(a0, a1, reinterpret_cast<const GLint *>(array));
%End

    void glUniform2f(GLint location, GLfloat x, GLfloat y);
    void glUniform2fv(GLint location, GLsizei count, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform2fv(a0, a1, reinterpret_cast<const GLfloat *>(array));
%End

    void glUniform2i(GLint location, GLint x, GLint y);
    void glUniform2iv(GLint location, GLsizei count, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform2iv(a0, a1, reinterpret_cast<const GLint *>(array));
%End

    void glUniform3f(GLint location, GLfloat x, GLfloat y, GLfloat z);
    void glUniform3fv(GLint location, GLsizei count, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform3fv(a0, a1, reinterpret_cast<const GLfloat *>(array));
%End

    void glUniform3i(GLint location, GLint x, GLint y, GLint z);
    void glUniform3iv(GLint location, GLsizei count, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform3iv(a0, a1, reinterpret_cast<const GLint *>(array));
%End

    void glUniform4f(GLint location, GLfloat x, GLfloat y, GLfloat z, GLfloat w);
    void glUniform4fv(GLint location, GLsizei count, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform4fv(a0, a1, reinterpret_cast<const GLfloat *>(array));
%End

    void glUniform4i(GLint location, GLint x, GLint y, GLint z, GLint w);
    void glUniform4iv(GLint location, GLsizei count, SIP_PYOBJECT v /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform4iv(a0, a1, reinterpret_cast<const GLint *>(array));
%End

    void glUniformMatrix2fv(GLint location, GLsizei count, GLboolean transpose, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a3, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniformMatrix2fv(a0, a1, a2,
                    reinterpret_cast<const GLfloat *>(array));
%End

    void glUniformMatrix3fv(GLint location, GLsizei count, GLboolean transpose, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a3, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniformMatrix3fv(a0, a1, a2,
                    reinterpret_cast<const GLfloat *>(array));
%End

    void glUniformMatrix4fv(GLint location, GLsizei count, GLboolean transpose, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a3, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniformMatrix4fv(a0, a1, a2,
                    reinterpret_cast<const GLfloat *>(array));
%End

    void glUseProgram(GLuint program);
    void glValidateProgram(GLuint program);
    void glVertexAttrib1f(GLuint indx, GLfloat x);
    void glVertexAttrib1fv(GLuint indx, SIP_PYOBJECT values /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib1fv(a0, reinterpret_cast<const GLfloat *>(array));
%End

    void glVertexAttrib2f(GLuint indx, GLfloat x, GLfloat y);
    void glVertexAttrib2fv(GLuint indx, SIP_PYOBJECT values /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib2fv(a0, reinterpret_cast<const GLfloat *>(array));
%End

    void glVertexAttrib3f(GLuint indx, GLfloat x, GLfloat y, GLfloat z);
    void glVertexAttrib3fv(GLuint indx, SIP_PYOBJECT values /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib3fv(a0, reinterpret_cast<const GLfloat *>(array));
%End

    void glVertexAttrib4f(GLuint indx, GLfloat x, GLfloat y, GLfloat z, GLfloat w);
    void glVertexAttrib4fv(GLuint indx, SIP_PYOBJECT values /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttrib4fv(a0, reinterpret_cast<const GLfloat *>(array));
%End

    void glVertexAttribPointer(GLuint indx, GLint size, GLenum type, GLboolean normalized, GLsizei stride, SIP_PYOBJECT ptr /TypeHint="PYQT_OPENGL_BOUND_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array_cached(&sipError, a5, a2, sipSelf,
                "VertexAttribPointer", a0);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttribPointer(a0, a1, a2, a3, a4, array);
%End

    void glViewport(GLint x, GLint y, GLsizei width, GLsizei height);
};

%End
