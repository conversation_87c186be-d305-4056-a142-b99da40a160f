// qopengltimerquery.sip generated by MetaSIP
//
// This file is part of the QtOpenGL Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (!PyQt_OpenGL_ES2)

class QOpenGLTimerQuery : public QObject
{
%TypeHeaderCode
#include <qopengltimerquery.h>
%End

public:
    explicit QOpenGLTimerQuery(QObject *parent /TransferThis/ = 0);
    virtual ~QOpenGLTimerQuery();
    bool create();
    void destroy();
    bool isCreated() const;
    GLuint objectId() const;
    void begin();
    void end();
    GLuint64 waitForTimestamp() const /ReleaseGIL/;
    void recordTimestamp();
    bool isResultAvailable() const;
    GLuint64 waitForResult() const /ReleaseGIL/;
};

%End
%If (!PyQt_OpenGL_ES2)

class QOpenGLTimeMonitor : public QObject
{
%TypeHeaderCode
#include <qopengltimerquery.h>
%End

public:
    explicit QOpenGLTimeMonitor(QObject *parent /TransferThis/ = 0);
    virtual ~QOpenGLTimeMonitor();
    void setSampleCount(int sampleCount);
    int sampleCount() const;
    bool create();
    void destroy();
    bool isCreated() const;
    QList<unsigned int> objectIds() const;
    int recordSample();
    bool isResultAvailable() const;
    QList<GLuint64> waitForSamples() const /ReleaseGIL/;
    QList<GLuint64> waitForIntervals() const /ReleaseGIL/;
    void reset();
};

%End
