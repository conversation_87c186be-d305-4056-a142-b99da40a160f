// qhelpsearchengine.sip generated by MetaSIP
//
// This file is part of the QtHelp Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QHelpSearchQuery
{
%TypeHeaderCode
#include <qhelpsearchengine.h>
%End

public:
    enum FieldName
    {
        DEFAULT,
        FUZZY,
        WITHOUT,
        PHRASE,
        ALL,
        ATLEAST,
    };

    QHelpSearchQuery();
    QHelpSearchQuery(QHelpSearchQuery::FieldName field, const QStringList &wordList);
};

class QHelpSearchEngine : public QObject
{
%TypeHeaderCode
#include <qhelpsearchengine.h>
%End

public:
    QHelpSearchEngine(QHelpEngineCore *helpEngine, QObject *parent /TransferThis/ = 0);
    virtual ~QHelpSearchEngine();
    QHelpSearchQueryWidget *queryWidget();
    QHelpSearchResultWidget *resultWidget();

public slots:
    void reindexDocumentation();
    void cancelIndexing();
    void cancelSearching();

signals:
    void indexingStarted();
    void indexingFinished();
    void searchingStarted();
    void searchingFinished(int hits);

public:
    int searchResultCount() const;
    QList<QHelpSearchResult> searchResults(int start, int end) const;
    QString searchInput() const;

public slots:
    void search(const QString &searchInput);
};

%If (- Qt_6_8_0)

class QHelpSearchResult
{
%TypeHeaderCode
#include <qhelpsearchengine.h>
%End

public:
    QHelpSearchResult();
    QHelpSearchResult(const QHelpSearchResult &other);
    QHelpSearchResult(const QUrl &url, const QString &title, const QString &snippet);
    ~QHelpSearchResult();
    QString title() const;
    QUrl url() const;
    QString snippet() const;
};

%End
