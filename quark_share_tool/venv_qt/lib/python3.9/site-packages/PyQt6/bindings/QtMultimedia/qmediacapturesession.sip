// qmediacapturesession.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QMediaCaptureSession : public QObject
{
%TypeHeaderCode
#include <qmediacapturesession.h>
%End

public:
    explicit QMediaCaptureSession(QObject *parent /TransferThis/ = 0);
    virtual ~QMediaCaptureSession();
    QAudioInput *audioInput() const;
    void setAudioInput(QAudioInput *device);
    QCamera *camera() const;
    void setCamera(QCamera *camera);
    QImageCapture *imageCapture();
    void setImageCapture(QImageCapture *imageCapture);
    QMediaRecorder *recorder();
    void setRecorder(QMediaRecorder *recorder);
    void setVideoOutput(QObject *output);
    QObject *videoOutput() const;
    void setVideoSink(QVideoSink *sink);
    QVideoSink *videoSink() const;
    void setAudioOutput(QAudioOutput *output);
    QAudioOutput *audioOutput() const;

signals:
    void audioInputChanged();
    void cameraChanged();
    void imageCaptureChanged();
    void recorderChanged();
    void videoOutputChanged();
    void audioOutputChanged();

public:
%If (Qt_6_5_0 -)
    QScreenCapture *screenCapture();
%End
%If (Qt_6_5_0 -)
    void setScreenCapture(QScreenCapture *screenCapture);
%End

signals:
%If (Qt_6_5_0 -)
    void screenCaptureChanged();
%End

public:
%If (Qt_6_6_0 -)
    QWindowCapture *windowCapture();
%End
%If (Qt_6_6_0 -)
    void setWindowCapture(QWindowCapture *windowCapture);
%End

signals:
%If (Qt_6_6_0 -)
    void windowCaptureChanged();
%End

public:
%If (Qt_6_8_0 -)
    QAudioBufferInput *audioBufferInput() const;
%End
%If (Qt_6_8_0 -)
    void setAudioBufferInput(QAudioBufferInput *input);
%End
%If (Qt_6_8_0 -)
    QVideoFrameInput *videoFrameInput() const;
%End
%If (Qt_6_8_0 -)
    void setVideoFrameInput(QVideoFrameInput *input);
%End

signals:
%If (Qt_6_8_0 -)
    void audioBufferInputChanged();
%End
%If (Qt_6_8_0 -)
    void videoFrameInputChanged();
%End
};

%End
