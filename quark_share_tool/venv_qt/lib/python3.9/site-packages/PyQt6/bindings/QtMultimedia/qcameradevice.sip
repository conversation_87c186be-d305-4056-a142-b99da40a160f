// qcameradevice.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QCameraFormat
{
%TypeHeaderCode
#include <qcameradevice.h>
%End

public:
    QCameraFormat();
    QCameraFormat(const QCameraFormat &other);
    ~QCameraFormat();
    QVideoFrameFormat::PixelFormat pixelFormat() const;
    QSize resolution() const;
    float minFrameRate() const;
    float maxFrameRate() const;
    bool isNull() const;
    bool operator==(const QCameraFormat &other) const;
    bool operator!=(const QCameraFormat &other) const;
};

%End
%If (Qt_6_2_0 -)

class QCameraDevice
{
%TypeHeaderCode
#include <qcameradevice.h>
%End

public:
    QCameraDevice();
    QCameraDevice(const QCameraDevice &other);
    ~QCameraDevice();
    bool operator==(const QCameraDevice &other) const;
    bool operator!=(const QCameraDevice &other) const;
    bool isNull() const;
    QByteArray id() const;
    QString description() const;
    bool isDefault() const;

    enum Position
    {
        UnspecifiedPosition,
        BackFace,
        FrontFace,
    };

    QCameraDevice::Position position() const;
    QList<QSize> photoResolutions() const;
    QList<QCameraFormat> videoFormats() const;
%If (Qt_6_7_0 -)
    QtVideo::Rotation correctionAngle() const;
%End
};

%End
