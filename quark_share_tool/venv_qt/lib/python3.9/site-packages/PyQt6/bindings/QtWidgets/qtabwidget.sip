// qtabwidget.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTabWidget : public QWidget
{
%TypeHeaderCode
#include <qtabwidget.h>
%End

public:
    explicit QTabWidget(QWidget *parent /TransferThis/ = 0);
    virtual ~QTabWidget();
    void clear();
    int addTab(QWidget *widget /Transfer/, const QString &);
    int addTab(QWidget *widget /Transfer/, const QIcon &icon, const QString &label);
    int insertTab(int index, QWidget *widget /Transfer/, const QString &);
    int insertTab(int index, QWidget *widget /Transfer/, const QIcon &icon, const QString &label);
    void removeTab(int index);
    bool isTabEnabled(int index) const;
    void setTabEnabled(int index, bool);
    QString tabText(int index) const;
    void setTabText(int index, const QString &);
    QIcon tabIcon(int index) const;
    void setTabIcon(int index, const QIcon &icon);
    void setTabToolTip(int index, const QString &tip);
    QString tabToolTip(int index) const;
    void setTabWhatsThis(int index, const QString &text);
    QString tabWhatsThis(int index) const;
    int currentIndex() const;
    QWidget *currentWidget() const;
    QWidget *widget(int index) const;
    int indexOf(const QWidget *widget) const;
    int count() const /__len__/;

    enum TabPosition
    {
        North,
        South,
        West,
        East,
    };

    QTabWidget::TabPosition tabPosition() const;
    void setTabPosition(QTabWidget::TabPosition);

    enum TabShape
    {
        Rounded,
        Triangular,
    };

    QTabWidget::TabShape tabShape() const;
    void setTabShape(QTabWidget::TabShape s);
    virtual QSize sizeHint() const;
    virtual QSize minimumSizeHint() const;
    void setCornerWidget(QWidget *widget /Transfer/, Qt::Corner corner = Qt::TopRightCorner);
    QWidget *cornerWidget(Qt::Corner corner = Qt::TopRightCorner) const;

public slots:
    void setCurrentIndex(int index);
    void setCurrentWidget(QWidget *widget);

signals:
    void currentChanged(int index);

protected:
    virtual void initStyleOption(QStyleOptionTabWidgetFrame *option) const;
    virtual void tabInserted(int index);
    virtual void tabRemoved(int index);
    virtual bool event(QEvent *);
    virtual void showEvent(QShowEvent *);
    virtual void resizeEvent(QResizeEvent *);
    virtual void keyPressEvent(QKeyEvent *);
    virtual void paintEvent(QPaintEvent *);
    void setTabBar(QTabBar * /Transfer/);

public:
    QTabBar *tabBar() const;

protected:
    virtual void changeEvent(QEvent *);

public:
    Qt::TextElideMode elideMode() const;
    void setElideMode(Qt::TextElideMode);
    QSize iconSize() const;
    void setIconSize(const QSize &size);
    bool usesScrollButtons() const;
    void setUsesScrollButtons(bool useButtons);
    bool tabsClosable() const;
    void setTabsClosable(bool closeable);
    bool isMovable() const;
    void setMovable(bool movable);
    bool documentMode() const;
    void setDocumentMode(bool set);

signals:
    void tabCloseRequested(int index);

public:
    virtual int heightForWidth(int width) const;
    virtual bool hasHeightForWidth() const;

signals:
    void tabBarClicked(int index);
    void tabBarDoubleClicked(int index);

public:
    bool tabBarAutoHide() const;
    void setTabBarAutoHide(bool enabled);
    bool isTabVisible(int index) const;
    void setTabVisible(int index, bool visible);
};
