// qprogressdialog.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QProgressDialog : public QDialog
{
%TypeHeaderCode
#include <qprogressdialog.h>
%End

public:
    QProgressDialog(QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    QProgressDialog(const QString &labelText, const QString &cancelButtonText, int minimum, int maximum, QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    virtual ~QProgressDialog();
    void setLabel(QLabel *label /Transfer/);
    void setCancelButton(QPushButton *button /Transfer/);
    void setBar(QProgressBar *bar /Transfer/);
    bool wasCanceled() const;
    int minimum() const;
    int maximum() const;
    void setRange(int minimum, int maximum);
    int value() const;
    virtual QSize sizeHint() const;
    QString labelText() const;
    int minimumDuration() const;
    void setAutoReset(bool b);
    bool autoReset() const;
    void setAutoClose(bool b);
    bool autoClose() const;

public slots:
    void cancel();
    void reset();
    void setMaximum(int maximum);
    void setMinimum(int minimum);
    void setValue(int progress);
    void setLabelText(const QString &);
    void setCancelButtonText(const QString &);
    void setMinimumDuration(int ms);

signals:
    void canceled();

protected:
    virtual void resizeEvent(QResizeEvent *);
    virtual void closeEvent(QCloseEvent *);
    virtual void changeEvent(QEvent *);
    virtual void showEvent(QShowEvent *e);
    void forceShow();

public:
    void open();
    void open(SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/);
%MethodCode
        QObject *receiver;
        QByteArray slot_signature;
        
        if ((sipError = pyqt6_qtwidgets_get_connection_parts(a0, sipCpp, "()", false, &receiver, slot_signature)) == sipErrorNone)
        {
            sipCpp->open(receiver, slot_signature.constData());
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(0, a0);
        }
%End
};
