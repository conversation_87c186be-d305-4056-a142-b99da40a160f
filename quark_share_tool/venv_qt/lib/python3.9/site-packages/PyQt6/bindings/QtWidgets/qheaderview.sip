// qheaderview.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QHeaderView : public QAbstractItemView
{
%TypeHeaderCode
#include <qheaderview.h>
%End

public:
    enum ResizeMode
    {
        Interactive,
        Fixed,
        Stretch,
        ResizeToContents,
        Custom,
    };

    QHeaderView(Qt::Orientation orientation, QWidget *parent /TransferThis/ = 0);
    virtual ~QHeaderView();
    virtual void setModel(QAbstractItemModel *model /KeepReference/);
    Qt::Orientation orientation() const;
    int offset() const;
    int length() const;
    virtual QSize sizeHint() const;
    int sectionSizeHint(int logicalIndex) const;
    int visualIndexAt(int position) const;
    int logicalIndexAt(int position) const;
    int sectionSize(int logicalIndex) const;
    int sectionPosition(int logicalIndex) const;
    int sectionViewportPosition(int logicalIndex) const;
    void moveSection(int from, int to);
    void resizeSection(int logicalIndex, int size);
    bool isSectionHidden(int logicalIndex) const;
    void setSectionHidden(int logicalIndex, bool hide);
    int count() const /__len__/;
    int visualIndex(int logicalIndex) const;
    int logicalIndex(int visualIndex) const;
    void setHighlightSections(bool highlight);
    bool highlightSections() const;
    int stretchSectionCount() const;
    void setSortIndicatorShown(bool show);
    bool isSortIndicatorShown() const;
    void setSortIndicator(int logicalIndex, Qt::SortOrder order);
    int sortIndicatorSection() const;
    Qt::SortOrder sortIndicatorOrder() const;
    bool stretchLastSection() const;
    void setStretchLastSection(bool stretch);
    bool sectionsMoved() const;

public slots:
    void setOffset(int offset);
    void headerDataChanged(Qt::Orientation orientation, int logicalFirst, int logicalLast);
    void setOffsetToSectionPosition(int visualIndex);

signals:
    void geometriesChanged();
    void sectionMoved(int logicalIndex, int oldVisualIndex, int newVisualIndex);
    void sectionResized(int logicalIndex, int oldSize, int newSize);
    void sectionPressed(int logicalIndex);
    void sectionClicked(int logicalIndex);
    void sectionDoubleClicked(int logicalIndex);
    void sectionCountChanged(int oldCount, int newCount);
    void sectionHandleDoubleClicked(int logicalIndex);

protected slots:
    void updateSection(int logicalIndex);
    void resizeSections();
    void sectionsInserted(const QModelIndex &parent, int logicalFirst, int logicalLast);
    void sectionsAboutToBeRemoved(const QModelIndex &parent, int logicalFirst, int logicalLast);

protected:
    void initialize();
    void initializeSections();
    void initializeSections(int start, int end);
    virtual void currentChanged(const QModelIndex &current, const QModelIndex &old);
    virtual bool event(QEvent *e);
    virtual bool viewportEvent(QEvent *e);
    virtual void paintEvent(QPaintEvent *e);
    virtual void mousePressEvent(QMouseEvent *e);
    virtual void mouseMoveEvent(QMouseEvent *e);
    virtual void mouseReleaseEvent(QMouseEvent *e);
    virtual void mouseDoubleClickEvent(QMouseEvent *e);
    virtual void paintSection(QPainter *painter, const QRect &rect, int logicalIndex) const;
    virtual QSize sectionSizeFromContents(int logicalIndex) const;
    virtual int horizontalOffset() const;
    virtual int verticalOffset() const;
    virtual void updateGeometries();
    virtual void scrollContentsBy(int dx, int dy);
    virtual void dataChanged(const QModelIndex &topLeft, const QModelIndex &bottomRight, const QList<int> &roles = QList<int>());
    virtual void rowsInserted(const QModelIndex &parent, int start, int end);
    virtual QRect visualRect(const QModelIndex &index) const;
    virtual void scrollTo(const QModelIndex &index, QAbstractItemView::ScrollHint hint);
    virtual QModelIndex indexAt(const QPoint &p) const;
    virtual bool isIndexHidden(const QModelIndex &index) const;
    virtual QModelIndex moveCursor(QAbstractItemView::CursorAction, Qt::KeyboardModifiers);
    virtual void setSelection(const QRect &rect, QItemSelectionModel::SelectionFlags flags);
    virtual QRegion visualRegionForSelection(const QItemSelection &selection) const;

public:
    int logicalIndexAt(int ax, int ay) const;
    int logicalIndexAt(const QPoint &apos) const;
    void hideSection(int alogicalIndex);
    void showSection(int alogicalIndex);
    void resizeSections(QHeaderView::ResizeMode mode);
    int hiddenSectionCount() const;
    int defaultSectionSize() const;
    void setDefaultSectionSize(int size);
    Qt::Alignment defaultAlignment() const;
    void setDefaultAlignment(Qt::Alignment alignment);
    bool sectionsHidden() const;
    void swapSections(int first, int second);
    bool cascadingSectionResizes() const;
    void setCascadingSectionResizes(bool enable);
    int minimumSectionSize() const;
    void setMinimumSectionSize(int size);
    QByteArray saveState() const;
    bool restoreState(const QByteArray &state);
    virtual void reset();

public slots:
    void setOffsetToLastSection();

signals:
    void sectionEntered(int logicalIndex);
    void sortIndicatorChanged(int logicalIndex, Qt::SortOrder order);

protected:
    virtual void initStyleOption(QStyleOptionHeader *option) const;
    virtual void initStyleOptionForIndex(QStyleOptionHeader *option, int logicalIndex) const;

public:
    void setSectionsMovable(bool movable);
    bool sectionsMovable() const;
    void setSectionsClickable(bool clickable);
    bool sectionsClickable() const;
    QHeaderView::ResizeMode sectionResizeMode(int logicalIndex) const;
    void setSectionResizeMode(int logicalIndex, QHeaderView::ResizeMode mode);
    void setSectionResizeMode(QHeaderView::ResizeMode mode);
    virtual void setVisible(bool v);
    void setResizeContentsPrecision(int precision);
    int resizeContentsPrecision() const;
    int maximumSectionSize() const;
    void setMaximumSectionSize(int size);
    void resetDefaultSectionSize();
    void setFirstSectionMovable(bool movable);
    bool isFirstSectionMovable() const;
%If (Qt_6_1_0 -)
    void setSortIndicatorClearable(bool clearable);
%End
%If (Qt_6_1_0 -)
    bool isSortIndicatorClearable() const;
%End

signals:
%If (Qt_6_1_0 -)
    void sortIndicatorClearableChanged(bool clearable);
%End
};
