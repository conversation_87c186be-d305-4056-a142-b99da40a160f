// qsqlfield.sip generated by MetaSIP
//
// This file is part of the QtSql Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSqlField
{
%TypeHeaderCode
#include <qsqlfield.h>
%End

public:
    enum RequiredStatus
    {
        Unknown,
        Optional,
        Required,
    };

    QSqlField(const QString &fieldName = QString(), QMetaType type = QMetaType(), const QString &tableName = QString());
    QSqlField(const QSqlField &other);
    bool operator==(const QSqlField &other) const;
    bool operator!=(const QSqlField &other) const;
    ~QSqlField();
    void setValue(const QVariant &value);
    QVariant value() const;
    void setName(const QString &name);
    QString name() const;
    bool isNull() const;
    void setReadOnly(bool readOnly);
    bool isReadOnly() const;
    void clear();
    bool isAutoValue() const;
    void setRequiredStatus(QSqlField::RequiredStatus status);
    void setRequired(bool required);
    void setLength(int fieldLength);
    void setPrecision(int precision);
    void setDefaultValue(const QVariant &value);
    void setSqlType(int type);
    void setGenerated(bool gen);
    void setAutoValue(bool autoVal);
    QSqlField::RequiredStatus requiredStatus() const;
    int length() const;
    int precision() const;
    QVariant defaultValue() const;
    int typeID() const;
    bool isGenerated() const;
    bool isValid() const;
    void setTableName(const QString &tableName);
    QString tableName() const;
    QMetaType metaType() const;
    void setMetaType(QMetaType type);
%If (Qt_6_6_0 -)
    void swap(QSqlField &other /Constrained/);
%End
};
