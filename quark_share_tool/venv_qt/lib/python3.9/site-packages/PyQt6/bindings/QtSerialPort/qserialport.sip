// qserialport.sip generated by MetaSIP
//
// This file is part of the QtSerialPort Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QSerialPort : public QIODevice
{
%TypeHeaderCode
#include <qserialport.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QSerialPort, &sipType_QSerialPort, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum Direction /BaseType=Flag/
    {
        Input,
        Output,
        AllDirections,
    };

    typedef QFlags<QSerialPort::Direction> Directions;

    enum BaudRate
    {
        Baud1200,
        Baud2400,
        Baud4800,
        Baud9600,
        Baud19200,
        Baud38400,
        Baud57600,
        Baud115200,
    };

    enum DataBits
    {
        Data5,
        Data6,
        Data7,
        Data8,
    };

    enum Parity
    {
        NoParity,
        EvenParity,
        OddParity,
        SpaceParity,
        MarkParity,
    };

    enum StopBits
    {
        OneStop,
        OneAndHalfStop,
        TwoStop,
    };

    enum FlowControl
    {
        NoFlowControl,
        HardwareControl,
        SoftwareControl,
    };

    enum PinoutSignal /BaseType=Flag/
    {
        NoSignal,
        DataTerminalReadySignal,
        DataCarrierDetectSignal,
        DataSetReadySignal,
        RingIndicatorSignal,
        RequestToSendSignal,
        ClearToSendSignal,
        SecondaryTransmittedDataSignal,
        SecondaryReceivedDataSignal,
    };

    typedef QFlags<QSerialPort::PinoutSignal> PinoutSignals;

    enum SerialPortError
    {
        NoError,
        DeviceNotFoundError,
        PermissionError,
        OpenError,
        WriteError,
        ReadError,
        ResourceError,
        UnsupportedOperationError,
        TimeoutError,
        NotOpenError,
        UnknownError,
    };

    explicit QSerialPort(QObject *parent /TransferThis/ = 0);
    QSerialPort(const QString &name, QObject *parent /TransferThis/ = 0);
    QSerialPort(const QSerialPortInfo &info, QObject *parent /TransferThis/ = 0);
    virtual ~QSerialPort();
    void setPortName(const QString &name);
    QString portName() const;
    void setPort(const QSerialPortInfo &info);
    virtual bool open(QIODeviceBase::OpenMode mode) /ReleaseGIL/;
    virtual void close() /ReleaseGIL/;
    bool setBaudRate(qint32 baudRate, QSerialPort::Directions dir = QSerialPort::AllDirections);
    qint32 baudRate(QSerialPort::Directions dir = QSerialPort::AllDirections) const;
    bool setDataBits(QSerialPort::DataBits dataBits);
    QSerialPort::DataBits dataBits() const;
    bool setParity(QSerialPort::Parity parity);
    QSerialPort::Parity parity() const;
    bool setStopBits(QSerialPort::StopBits stopBits);
    QSerialPort::StopBits stopBits() const;
    bool setFlowControl(QSerialPort::FlowControl flow);
    QSerialPort::FlowControl flowControl() const;
    bool setDataTerminalReady(bool set);
    bool isDataTerminalReady();
    bool setRequestToSend(bool set);
    bool isRequestToSend();
    QSerialPort::PinoutSignals pinoutSignals();
    bool flush() /ReleaseGIL/;
    bool clear(QSerialPort::Directions dir = QSerialPort::AllDirections);
    QSerialPort::SerialPortError error() const;
    void clearError();
    qint64 readBufferSize() const;
    void setReadBufferSize(qint64 size);
    virtual bool isSequential() const;
    virtual qint64 bytesAvailable() const;
    virtual qint64 bytesToWrite() const;
    virtual bool canReadLine() const;
    virtual bool waitForReadyRead(int msecs = 30000) /ReleaseGIL/;
    virtual bool waitForBytesWritten(int msecs = 30000) /ReleaseGIL/;
    bool setBreakEnabled(bool enabled = true);

signals:
    void baudRateChanged(qint32 baudRate, QSerialPort::Directions directions);
    void dataBitsChanged(QSerialPort::DataBits dataBits);
    void parityChanged(QSerialPort::Parity parity);
    void stopBitsChanged(QSerialPort::StopBits stopBits);
    void flowControlChanged(QSerialPort::FlowControl flow);
    void dataTerminalReadyChanged(bool set);
    void requestToSendChanged(bool set);

protected:
    virtual SIP_PYOBJECT readData(qint64 maxlen) /TypeHint="bytes",ReleaseGIL/ [qint64 (char *data, qint64 maxSize)];
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            len = sipSelfWasArg ? sipCpp->QSerialPort::readData(s, a0) : sipCpp->readData(s, a0);
        #else
            len = sipCpp->sipProtectVirt_readData(sipSelfWasArg, s, a0);
        #endif
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = PyBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

    virtual SIP_PYOBJECT readLineData(qint64 maxlen) /TypeHint="bytes",ReleaseGIL/ [qint64 (char *data, qint64 maxSize)];
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            len = sipSelfWasArg ? sipCpp->QSerialPort::readLineData(s, a0) : sipCpp->readLineData(s, a0);
        #else
            len = sipCpp->sipProtectVirt_readLineData(sipSelfWasArg, s, a0);
        #endif
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = PyBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

    virtual qint64 writeData(const char *data /Array/, qint64 maxSize /ArraySize/) /ReleaseGIL/;

public:
%If (Windows)
    void *handle() const;
%End
%If (Android || Linux || iOS || macOS || WebAssembly)
    int handle() const;
%End
    bool isBreakEnabled() const;

signals:
    void breakEnabledChanged(bool set);
    void errorOccurred(QSerialPort::SerialPortError error);

public:
%If (Qt_6_9_0 -)
    bool settingsRestoredOnClose() const;
%End
%If (Qt_6_9_0 -)
    void setSettingsRestoredOnClose(bool restore);
%End

signals:
%If (Qt_6_9_0 -)
    void settingsRestoredOnCloseChanged(bool restore);
%End
};

%End
