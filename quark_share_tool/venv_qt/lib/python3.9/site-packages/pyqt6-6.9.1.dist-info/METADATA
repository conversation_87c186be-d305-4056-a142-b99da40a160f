Metadata-Version: 2.4
Name: PyQt6
Version: 6.9.1
Requires-Python: >=3.9
Summary: Python bindings for the Qt cross platform application toolkit
Description-Content-Type: text/markdown
Project-Url: homepage, https://www.riverbankcomputing.com/software/pyqt/
Requires-Dist: PyQt6-sip (>=13.8, <14)
Requires-Dist: PyQt6-Qt6 (>=6.9.0, <6.10.0)
License-Expression: GPL-3.0-only
License-File: LICENSE
Author-Email: Riverbank Computing Limited <<EMAIL>>

# PyQt6 - Comprehensive Python Bindings for Qt v6

Qt is set of cross-platform C++ libraries that implement high-level APIs for
accessing many aspects of modern desktop and mobile systems.  These include
location and positioning services, multimedia, NFC and Bluetooth connectivity,
a Chromium based web browser, as well as traditional UI development.

PyQt6 is a comprehensive set of Python bindings for Qt v6.  It is implemented
as more than 35 extension modules and enables Python to be used as an
alternative application development language to C++ on all supported platforms
including iOS and Android.

PyQt6 may also be embedded in C++ based applications to allow users of those
applications to configure or enhance the functionality of those applications.


## Author

PyQt6 is copyright (c) Riverbank Computing Limited.  Its homepage is
https://www.riverbankcomputing.com/software/pyqt/.

Support may be obtained from the PyQt mailing list at
https://www.riverbankcomputing.com/mailman/listinfo/pyqt/.


## License

PyQt6 is released under the GPL v3 license and under a commercial license that
allows for the development of proprietary applications.


## Documentation

The documentation for the latest release can be found
[here](https://www.riverbankcomputing.com/static/Docs/PyQt6/).


## Installation

The GPL version of PyQt6 can be installed from PyPI:

    pip install PyQt6

`pip` will also build and install the bindings from the sdist package but Qt's
`qmake` tool must be on `PATH`.

The `sip-install` tool will also install the bindings from the sdist package
but will allow you to configure many aspects of the installation.
