# 🌐 网页端登录使用说明

## 🎯 网页端API优势

### ✨ **为什么选择网页端模式？**

1. **🚀 速度更快** - 直接使用浏览器，无需复杂的API配置
2. **🔐 登录方便** - 支持扫码登录、短信验证等多种方式
3. **💾 保持登录** - 自动保存登录状态，下次启动无需重新登录
4. **🛡️ 更稳定** - 使用真实浏览器环境，兼容性更好
5. **🔧 易配置** - 无需复杂的API密钥配置

### 📊 **模式对比**

| 特性 | 网页端模式 | 原生API模式 | 演示模式 |
|------|------------|-------------|----------|
| 速度 | ⚡ 快 | 🐌 慢 | ⚡ 最快 |
| 登录方式 | 🔐 多种方式 | 📝 用户名密码 | ❌ 无需登录 |
| 配置复杂度 | 🟢 简单 | 🔴 复杂 | 🟢 无需配置 |
| 稳定性 | 🟢 稳定 | 🟡 一般 | 🟢 稳定 |
| 真实数据 | ✅ 是 | ✅ 是 | ❌ 否 |

## 🚀 快速开始

### 1️⃣ **切换到网页端模式**

#### 方法一：通过设置界面
1. **启动程序**
2. **点击"⚙️ 设置"按钮**
3. **选择"运行模式"选项卡**
4. **选择"🌐 网页端模式 (推荐)"**
5. **点击"应用"**
6. **重启程序**

#### 方法二：首次启动
- 程序默认使用网页端模式，无需额外配置

### 2️⃣ **登录夸克网盘**

#### 🔐 **支持的登录方式**

1. **扫码登录** (推荐)
   - 点击"🔑 登录"按钮
   - 程序会打开浏览器窗口
   - 使用手机夸克APP扫码登录

2. **手机号登录**
   - 输入手机号
   - 接收短信验证码
   - 完成登录

3. **邮箱登录**
   - 输入邮箱地址
   - 输入密码
   - 完成登录

4. **第三方登录**
   - 支持微信、QQ等第三方登录

### 3️⃣ **登录流程详解**

#### 🖱️ **操作步骤**

1. **启动程序**
   ```
   ./start_qt.sh  # macOS/Linux
   start_qt.bat   # Windows
   ```

2. **点击登录按钮**
   - 界面左上角的"🔑 登录"按钮

3. **浏览器自动打开**
   - 程序会自动打开Chrome浏览器
   - 导航到夸克网盘登录页面

4. **选择登录方式**
   - **扫码登录**: 使用手机APP扫码
   - **账号登录**: 输入用户名密码
   - **其他方式**: 短信验证、第三方登录等

5. **完成登录**
   - 登录成功后，点击程序提示对话框的"确定"
   - 程序会自动检测登录状态

6. **开始使用**
   - 登录成功后，程序会自动加载文件列表
   - 可以开始进行文件分享操作

### 🔧 **登录问题排除**

#### ❓ **浏览器无法打开**
**解决方案:**
1. 确保已安装Chrome浏览器
2. 检查网络连接
3. 重启程序重试

#### ❓ **登录页面显示异常**
**解决方案:**
1. 清除浏览器缓存
2. 检查网络代理设置
3. 尝试手动访问 https://pan.quark.cn

#### ❓ **扫码登录失败**
**解决方案:**
1. 确保手机夸克APP为最新版本
2. 检查手机网络连接
3. 重新生成二维码

#### ❓ **登录状态检测失败**
**解决方案:**
1. 手动刷新浏览器页面
2. 确认已成功登录到网盘主页
3. 点击程序对话框的"确定"按钮

### 💾 **登录状态保持**

#### 🔄 **自动保持登录**
- 程序会自动保存浏览器的登录状态
- 下次启动时无需重新登录
- 登录状态可保持数天到数周

#### 🗂️ **用户数据目录**
- 登录信息保存在: `~/.quark_browser_data`
- 包含Cookie、会话信息等
- 可以手动删除此目录来清除登录状态

#### 🔒 **安全提醒**
- 不要在公共电脑上保存登录状态
- 定期清理浏览器数据
- 使用完毕后及时登出

### 🎮 **使用技巧**

#### ⚡ **提升速度**
1. **保持浏览器开启**: 不要关闭自动打开的浏览器窗口
2. **网络优化**: 使用稳定的网络连接
3. **减少并发**: 避免同时进行大量操作

#### 🔧 **自定义配置**
1. **浏览器设置**: 可以在设置中调整浏览器参数
2. **超时时间**: 调整网络请求超时时间
3. **重试次数**: 设置失败重试次数

#### 📊 **监控状态**
1. **查看日志**: 检查 `logs/` 目录下的日志文件
2. **状态栏信息**: 关注程序底部状态栏提示
3. **浏览器控制台**: 查看浏览器开发者工具

### 🌟 **最佳实践**

#### 📋 **登录建议**
1. **首选扫码登录**: 最快最安全的登录方式
2. **保持网络稳定**: 避免登录过程中网络中断
3. **及时确认**: 登录成功后及时点击程序确认

#### 🔄 **使用建议**
1. **定期重新登录**: 建议每周重新登录一次
2. **监控登录状态**: 注意程序状态栏的登录提示
3. **备份重要数据**: 定期导出分享记录

#### 🛡️ **安全建议**
1. **使用个人设备**: 避免在公共设备上登录
2. **及时登出**: 使用完毕后点击登出按钮
3. **保护账户安全**: 不要泄露登录凭据

### 🎉 **开始体验**

现在您可以享受快速、便捷的网页端登录体验：

1. **🚀 启动程序** - 自动使用网页端模式
2. **🔑 点击登录** - 浏览器自动打开
3. **📱 扫码登录** - 使用手机APP扫码
4. **✅ 确认登录** - 点击程序确认对话框
5. **📁 开始使用** - 浏览文件，批量分享

### 📞 **技术支持**

如遇问题，请：
1. **查看日志文件**: `logs/` 目录
2. **检查网络连接**: 确保能访问夸克网盘
3. **重启程序**: 简单重启往往能解决问题
4. **清除缓存**: 删除 `~/.quark_browser_data` 目录

---

**🌐 网页端模式 - 让夸克网盘批量分享更快更简单！**

**版本**: v3.2 - 网页端登录版  
**更新日期**: 2024-06-24  
**推荐指数**: ⭐⭐⭐⭐⭐
