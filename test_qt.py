#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt6测试程序 - 检查基本功能
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QPushButton, QLabel
from PyQt6.QtCore import Qt

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PyQt6测试窗口")
        self.setGeometry(100, 100, 400, 300)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加标签
        label = QLabel("PyQt6测试程序")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label)
        
        # 添加按钮
        button = QPushButton("测试按钮")
        button.clicked.connect(self.on_button_click)
        layout.addWidget(button)
        
        # 应用深色主题样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                padding: 20px;
            }
            QPushButton {
                background-color: #404040;
                border: 1px solid #555;
                padding: 10px 20px;
                border-radius: 4px;
                color: #ffffff;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #505050;
            }
            QPushButton:pressed {
                background-color: #606060;
            }
        """)
    
    def on_button_click(self):
        print("按钮被点击了！")
        self.statusBar().showMessage("按钮点击成功！")

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("PyQt6测试")
    
    window = TestWindow()
    window.show()
    
    print("PyQt6测试窗口已启动")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
