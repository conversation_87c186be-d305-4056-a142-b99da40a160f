/* 输入框、搜索栏科技感 */
input[type="text"], input[type="search"], input[type="email"], input[type="password"], textarea {
  border: 1.5px solid var(--tag-glow-color) !important;
  border-radius: 6px !important;
  background: var(--tag-bg-dark);
  color: #bfeaff;
  box-shadow: 0 0 8px #7fd6ff22;
  padding: 7px 12px;
  font-size: 1rem;
  transition: border-color 0.3s, box-shadow 0.3s;
}
input[type="text"]:focus, input[type="search"]:focus, input[type="email"]:focus, input[type="password"]:focus, textarea:focus {
  border-color: #7fd6ff !important;
  box-shadow: 0 0 16px #7fd6ff88;
  outline: none;
}

/* 卡片/内容区块美化 */
.card, .post, .widget, .module, .ripro-box, .ripro-panel {
  background: #181d23;
  border-radius: 12px;
  box-shadow: 0 2px 24px #0a1a2a44;
  border: 1px solid #232c3a;
  padding: 18px 20px;
  margin-bottom: 18px;
}

/* 让所有发光元素更柔和 */
button, .btn, .share-btn, .fav-btn, .ripro-share, .ripro-fav,
input[type="text"], input[type="search"], input[type="email"], input[type="password"], textarea,
.tag, .tags a, .post-tag, .label, .badge {
  filter: brightness(1.08) saturate(1.1);
}

/* 适当缩小搜索按钮 */
input[type="submit"], .search-submit, .search-btn {
  padding: 5px 16px !important;
  font-size: 0.92rem !important;
  border-radius: 6px !important;
}

/* 适配夜间模式下的滚动条 */
::-webkit-scrollbar {
  width: 8px;
  background: #10151b;
}
::-webkit-scrollbar-thumb {
  background: #22384a;
  border-radius: 8px;
}

/* 其他细节优化可根据需要继续补充 */ 