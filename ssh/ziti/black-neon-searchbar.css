/* 搜索栏整体外框科技感发光 */
.search-form,
.search-bar,
.ripro-search-box,
.header-search,
.wp-block-search__inside-wrapper {
  background: var(--tag-bg-dark) !important;
  border: 1.5px solid var(--tag-glow-color) !important;
  border-radius: 8px !important;
  box-shadow: 0 0 12px #7fd6ff55, 0 0 24px #1a2a3a44;
  padding: 0.2em 0.5em;
  display: flex;
  align-items: center;
  transition: border-color 0.3s, box-shadow 0.3s;
}

/* 鼠标悬停或聚焦时更亮 */
.search-form:focus-within,
.search-bar:focus-within,
.ripro-search-box:focus-within,
.header-search:focus-within,
.wp-block-search__inside-wrapper:focus-within {
  border-color: #7fd6ff !important;
  box-shadow: 0 0 24px #7fd6ff, 0 0 48px #1a2a3a;
}

/* 下拉菜单按钮（如"全站"） */
.search-form select,
.search-bar select,
.ripro-search-box select,
.header-search select {
  background: #181d23;
  color: #bfeaff;
  border: none;
  border-radius: 6px;
  margin-right: 8px;
  font-size: 1rem;
  padding: 6px 12px;
  box-shadow: 0 0 8px #7fd6ff22;
  transition: background 0.3s, color 0.3s;
}

/* 下拉菜单悬停/聚焦 */
.search-form select:focus,
.search-bar select:focus,
.ripro-search-box select:focus,
.header-search select:focus {
  background: #22384a;
  color: #fff;
}

/* 搜索栏内的输入框和按钮继续用前面定义的科技感样式即可 */ 