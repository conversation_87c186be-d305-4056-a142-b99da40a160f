<?php
/*
Plugin Name: 自定义代码管理（中文）
Description: 支持多组自定义JS和CSS代码，界面全中文，简单易用，适合国人。
Version: 1.1
Author: AI助手
*/

add_action('admin_menu', function() {
    add_menu_page(
        '自定义代码管理', '自定义代码管理', 'manage_options', 'custom-code-chinese', 'custom_code_chinese_page', 'dashicons-welcome-widgets-menus', 80
    );
});

function custom_code_chinese_page() {
    // 读取所有代码组
    $groups = get_option('custom_code_chinese_groups', []);
    if (!is_array($groups)) $groups = [];
    $active_tab = isset($_GET['tab']) ? $_GET['tab'] : (count($groups) ? array_keys($groups)[0] : 'group1');

    // 处理表单提交
    if (isset($_POST['custom_code_chinese_save'])) {
        check_admin_referer('custom_code_chinese_save');
        $group_key = $_POST['group_key'];
        $groups[$group_key]['name'] = sanitize_text_field($_POST['group_name']);
        $groups[$group_key]['js'] = stripslashes($_POST['custom_code_chinese_js']);
        $groups[$group_key]['css'] = stripslashes($_POST['custom_code_chinese_css']);
        $groups[$group_key]['enabled'] = isset($_POST['custom_code_chinese_enabled']) ? 1 : 0;
        update_option('custom_code_chinese_groups', $groups);
        echo '<div class="updated"><p>保存成功！</p></div>';
        $active_tab = $group_key;
    }
    // 新建代码组
    if (isset($_POST['custom_code_chinese_newgroup'])) {
        $new_key = 'group' . (count($groups) + 1);
        $groups[$new_key] = [
            'name' => '新代码组',
            'js' => '',
            'css' => '',
            'enabled' => 0
        ];
        update_option('custom_code_chinese_groups', $groups);
        $active_tab = $new_key;
        echo '<div class="updated"><p>新建代码组成功！</p></div>';
    }
    // 删除代码组
    if (isset($_POST['custom_code_chinese_deletegroup'])) {
        $del_key = $_POST['group_key'];
        if (isset($groups[$del_key])) {
            unset($groups[$del_key]);
            update_option('custom_code_chinese_groups', $groups);
            $active_tab = count($groups) ? array_keys($groups)[0] : '';
            echo '<div class="updated"><p>删除成功！</p></div>';
        }
    }
    // 切换tab
    if (isset($_GET['tab'])) {
        $active_tab = $_GET['tab'];
    }
    // 渲染界面
    echo '<div class="wrap">';
    echo '<h2>自定义代码管理（多组，支持同时启用）</h2>';
    echo '<form method="post" style="margin-bottom:16px;display:inline-block;">';
    echo '<input type="submit" name="custom_code_chinese_newgroup" class="button" value="新建代码组">';
    echo '</form>';
    // Tab导航
    echo '<h3 style="margin-top:10px;">代码组：</h3>';
    echo '<nav style="margin-bottom:16px;">';
    foreach ($groups as $key => $group) {
        $active = ($key === $active_tab) ? 'font-weight:bold;text-decoration:underline;' : '';
        echo '<a href="?page=custom-code-chinese&tab=' . esc_attr($key) . '" style="margin-right:18px;'.$active.'">'.esc_html($group['name']).'</a>';
    }
    echo '</nav>';
    // 当前tab内容
    if ($active_tab && isset($groups[$active_tab])) {
        $group = $groups[$active_tab];
        echo '<form method="post">';
        wp_nonce_field('custom_code_chinese_save');
        echo '<input type="hidden" name="group_key" value="'.esc_attr($active_tab).'">';
        echo '<p><label>代码组名称：<input type="text" name="group_name" value="'.esc_attr($group['name']).'" style="width:200px;"></label></p>';
        echo '<p><label><input type="checkbox" name="custom_code_chinese_enabled" value="1" '.($group['enabled']?'checked':'').'> 启用本组代码</label></p>';
        echo '<h4>自定义 JavaScript 代码</h4>';
        echo '<textarea name="custom_code_chinese_js" rows="8" style="width:100%;font-family:monospace;">'.esc_textarea($group['js']).'</textarea>';
        echo '<h4>自定义 CSS 代码</h4>';
        echo '<textarea name="custom_code_chinese_css" rows="8" style="width:100%;font-family:monospace;">'.esc_textarea($group['css']).'</textarea>';
        echo '<p>';
        echo '<input type="submit" name="custom_code_chinese_save" class="button button-primary" value="保存"> ';
        echo '<input type="submit" name="custom_code_chinese_deletegroup" class="button" value="删除本组" onclick="return confirm(\'确定要删除本组吗？\');">';
        echo '</p>';
        echo '</form>';
    } else {
        echo '<p>暂无代码组，请先新建。</p>';
    }
    echo '<p style="color:#888;">本插件由AI助手为你定制，完全中文界面，安全无广告。支持多组代码管理，后续如需帮助，随时联系AI助手。</p>';
    echo '</div>';
}

// 输出所有启用的代码组到前台
function custom_code_chinese_output_all() {
    $groups = get_option('custom_code_chinese_groups', []);
    if (!is_array($groups)) return;
    foreach ($groups as $group) {
        if (!empty($group['enabled'])) {
            if (!empty($group['css'])) {
                echo "<style id='custom-code-chinese-css-".md5($group['name'])."'>\n" . $group['css'] . "\n</style>\n";
            }
            if (!empty($group['js'])) {
                echo "<script id='custom-code-chinese-js-".md5($group['name'])."'>\n" . $group['js'] . "\n</script>\n";
            }
        }
    }
}
add_action('wp_head', 'custom_code_chinese_output_all');
add_action('wp_footer', 'custom_code_chinese_output_all'); 