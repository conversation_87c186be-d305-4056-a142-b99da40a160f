document.addEventListener('DOMContentLoaded', () => {
    // Check which page we are on and run the corresponding setup function.
    if (document.body.id === 'page-index') {
        setupRegistrationPage();
    } else if (document.body.id === 'page-games') {
        // We'll restore the logic that reads user info from URL parameters
        setupGamesPage();
    }

    // A general function to load the star background on any page that needs it
    if (document.querySelector('.stars-bg')) {
        loadBackground();
    }
});


// --- Logic for Game Selection Page (games.html) ---
function setupGamesPage() {
    const gameListContainer = document.getElementById('game-list-container');
    const paginationControls = document.getElementById('pagination-controls');
    const searchBar = document.getElementById('search-bar');
    const submissionForm = document.getElementById('game-selection-form');
    const messageArea = document.getElementById('form-message');
    const selectionCounter = document.getElementById('selection-counter');

    // 新的购物车元素
    const cartToggle = document.getElementById('cart-toggle');
    const cartDropdown = document.getElementById('cart-dropdown');
    const cartCount = document.getElementById('cart-count');
    const cartItems = document.getElementById('cart-items');

    let allGames = [];
    let filteredGames = [];
    let selectedGames = new Map();
    let currentPage = 1;
    const gamesPerPage = 10;

    // --- 购物车功能 ---
    if (cartToggle && cartDropdown) {
        // 点击购物车图标切换下拉菜单
        cartToggle.addEventListener('click', (e) => {
            e.stopPropagation();
            cartDropdown.classList.toggle('show');
        });

        // 点击其他地方关闭下拉菜单
        document.addEventListener('click', (e) => {
            if (!cartDropdown.contains(e.target) && !cartToggle.contains(e.target)) {
                cartDropdown.classList.remove('show');
            }
        });

        // 初始化购物车显示
        updateCartDisplay();
    }
    
    // --- Get user info from URL ---
    const urlParams = new URLSearchParams(window.location.search);
    const userInfo = {
        phone: urlParams.get('phone'),
        serial: urlParams.get('serial'),
        order: urlParams.get('order')
    };

    if (!userInfo.phone) {
        console.error("User phone number not found in URL. Redirecting to home.");
        // Optionally, redirect back to the registration page if info is missing.
        // window.location.href = 'index.html';
        if(messageArea) {
             messageArea.textContent = '用户信息丢失，请返回首页重新登记。';
             messageArea.style.color = '#ff6b6b';
        }
        return;
    }


    // --- Core Rendering Functions ---
    function render() {
        renderGames();
        renderPagination();
        updateSelectionCount(); // 这个函数现在包含了购物车更新
    }

    function renderGames() {
        gameListContainer.innerHTML = '';
        if (filteredGames.length === 0) {
            gameListContainer.innerHTML = `<p style="text-align:center; grid-column: 1 / -1;">未找到匹配的游戏。</p>`;
            return;
        }

        const startIndex = (currentPage - 1) * gamesPerPage;
        const gamesOnPage = filteredGames.slice(startIndex, startIndex + gamesPerPage);

        gamesOnPage.forEach((game, index) => {
            const gameIndex = startIndex + index; // Use a truly unique index for the ID
            const displayName = game.name;
            const gameId = `game-${gameIndex}`; // GUARANTEED UNIQUE ID

            const gameItem = document.createElement('div');
            gameItem.className = 'game-item';
            if (selectedGames.has(displayName)) {
                gameItem.classList.add('selected');
            }

            gameItem.innerHTML = `
                <input type="checkbox" id="${gameId}" value="${displayName}" data-link="${game.link}" ${selectedGames.has(displayName) ? 'checked' : ''}>
                <label for="${gameId}">${displayName}</label>
            `;
            
            gameListContainer.appendChild(gameItem);
        });
    }

    function renderPagination() {
        paginationControls.innerHTML = '';
        const pageCount = Math.ceil(filteredGames.length / gamesPerPage);
        if (pageCount <= 1) return;

        const handlePageClick = (newPage) => {
            if (newPage < 1 || newPage > pageCount) return;
            currentPage = newPage;
            renderGames(); // Only need to re-render games and pagination
            renderPagination();
            // 移除滚动行为，防止背景移动
            // gameListContainer.scrollIntoView({ behavior: 'smooth' });
        };
        
        // Prev Button
        const prevBtn = document.createElement('button');
        prevBtn.textContent = '上一页';
        prevBtn.disabled = currentPage === 1;
        prevBtn.addEventListener('click', () => handlePageClick(currentPage - 1));
        paginationControls.appendChild(prevBtn);

        // Page numbers logic
        const pages = new Set([1, pageCount, currentPage, currentPage-1, currentPage+1]);
        const sortedPages = Array.from(pages).sort((a,b)=>a-b).filter(p => p > 0 && p <= pageCount);
        
        let lastPage = 0;
        sortedPages.forEach(p => {
             if (lastPage > 0 && p - lastPage > 1) {
                const ellipsis = document.createElement('span');
                ellipsis.className = 'pagination-ellipsis';
                ellipsis.textContent = '...';
                paginationControls.appendChild(ellipsis);
            }
            const btn = document.createElement('button');
            btn.textContent = p;
            if (p === currentPage) btn.classList.add('active');
            btn.addEventListener('click', () => handlePageClick(p));
            paginationControls.appendChild(btn);
            lastPage = p;
        });

        // Next Button
        const nextBtn = document.createElement('button');
        nextBtn.textContent = '下一页';
        nextBtn.disabled = currentPage === pageCount;
        nextBtn.addEventListener('click', () => handlePageClick(currentPage + 1));
        paginationControls.appendChild(nextBtn);

        // Jump to page input
        const jumpContainer = document.createElement('div');
        jumpContainer.className = 'page-jump-container';
        const pageInput = document.createElement('input');
        pageInput.type = 'number';
        pageInput.min = 1;
        pageInput.max = pageCount;
        pageInput.placeholder = `${currentPage}/${pageCount}`;
        const goBtn = document.createElement('button');
        goBtn.textContent = '跳转';
        goBtn.addEventListener('click', () => {
            const pageNum = parseInt(pageInput.value, 10);
            if (pageNum >= 1 && pageNum <= pageCount) {
                handlePageClick(pageNum);
            }
        });
        pageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                goBtn.click();
            }
        });
        jumpContainer.appendChild(pageInput);
        jumpContainer.appendChild(goBtn);
        paginationControls.appendChild(jumpContainer);
    }

    function updateCartDisplay() {
        // 更新购物车数量
        if (cartCount) {
            cartCount.textContent = selectedGames.size;
            cartCount.classList.toggle('hidden', selectedGames.size === 0);
        }

        // 更新购物车内容
        if (cartItems) {
            cartItems.innerHTML = '';

            if (selectedGames.size === 0) {
                cartItems.innerHTML = '<div class="empty-cart">尚未选择任何游戏</div>';
                return;
            }

            selectedGames.forEach((gameData, gameName) => {
                const cartItem = document.createElement('div');
                cartItem.className = 'cart-item';

                const nameSpan = document.createElement('span');
                nameSpan.className = 'cart-item-name';
                nameSpan.textContent = gameName;

                const removeBtn = document.createElement('button');
                removeBtn.className = 'cart-item-remove';
                removeBtn.innerHTML = '&times;';
                removeBtn.title = '移除游戏';
                removeBtn.onclick = () => {
                    selectedGames.delete(gameName);
                    render(); // Full re-render to ensure consistency
                };

                cartItem.appendChild(nameSpan);
                cartItem.appendChild(removeBtn);
                cartItems.appendChild(cartItem);
            });
        }
    }

    function updateSelectionCount() {
        selectionCounter.textContent = `已选择 ${selectedGames.size} 个游戏`;
        updateCartDisplay(); // 更新购物车显示
    }

    // --- Event Handlers ---
    searchBar.addEventListener('input', (e) => {
        const searchTerm = e.target.value.toLowerCase();
        filteredGames = allGames.filter(game => game.name.toLowerCase().includes(searchTerm));
        currentPage = 1;
        render();
    });

    submissionForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        messageArea.textContent = '';
        const submitButton = document.getElementById('submit-selection');

        // USE THE USER INFO FROM THE URL, NOT A LOCAL INPUT
        if (!userInfo || !userInfo.phone) {
            messageArea.textContent = '用户信息丢失，无法提交。请返回首页重试。';
            messageArea.style.color = '#ff6b6b';
            return;
        }

        const gamesToSubmit = Array.from(selectedGames.values());
        if (gamesToSubmit.length === 0) {
            messageArea.textContent = '请至少选择一个游戏。';
            messageArea.style.color = '#ff6b6b';
            return;
        }

        submitButton.disabled = true;
        submitButton.textContent = '正在提交...';

        try {
            const response = await fetch('/submit', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                // Submit the full userInfo object
                body: JSON.stringify({ userInfo: userInfo, selectedGames: gamesToSubmit }),
            });

            const result = await response.json();
            if (result.success) {
                messageArea.textContent = '提交成功！';
                messageArea.style.color = '#66c0f4';
                // Optionally clear selection after successful submission
                // selectedGames.clear();
                // render();
            } else {
                messageArea.textContent = `提交失败：${result.message}`;
                messageArea.style.color = '#ff6b6b';
            }
        } catch (error) {
            console.error('Submission error:', error);
            messageArea.textContent = '提交时发生网络错误。';
            messageArea.style.color = '#ff6b6b';
        } finally {
            submitButton.disabled = false;
            submitButton.textContent = '确认提交';
        }
    });
    
    gameListContainer.addEventListener('change', (e) => {
        if (e.target.tagName === 'INPUT' && e.target.type === 'checkbox') {
            const checkbox = e.target;
            const gameName = checkbox.value;
            const gameLink = checkbox.dataset.link;

            if (checkbox.checked) {
                selectedGames.set(gameName, { name: gameName, link: gameLink });
            } else {
                selectedGames.delete(gameName);
            }
            // Visually update the item
            checkbox.closest('.game-item').classList.toggle('selected', checkbox.checked);
            updateSelectionCount(); // 这个函数现在包含了购物车更新
        }
    });
    
    // CORRECTED CLICK HANDLER
    gameListContainer.addEventListener('click', (e) => {
        // If the click is on the label, the browser handles the checking.
        // The 'change' event will fire naturally. So we do nothing.
        if (e.target.tagName === 'LABEL') {
            return;
        }

        const gameItem = e.target.closest('.game-item');
        if (!gameItem) return;

        // If the click was on the item's padding/div, but not on the checkbox or label
        if (e.target.tagName !== 'INPUT') {
            const checkbox = gameItem.querySelector('input[type="checkbox"]');
            if (checkbox) {
                // Manually toggle the checkbox state
                checkbox.checked = !checkbox.checked;
                // And then manually trigger the change event to update everything
                checkbox.dispatchEvent(new Event('change', { bubbles: true }));
            }
        }
    });

    // --- Initial Data Load ---
    async function init() {
        try {
            const response = await fetch('/api/games');
            if (!response.ok) throw new Error('Network response was not ok.');
            
            const rawGames = await response.json();
            // The API sends { chineseName, englishName, cover, link }
            // We need to adapt it to { name, link }
            allGames = rawGames.map(g => ({
                name: `${g.chineseName || ''} ${g.englishName || ''}`.trim(),
                link: g.link
            })).filter(g => g.name); // Filter out any empty-named games

            filteredGames = allGames;
            render();
        } catch (error) {
            console.error('Failed to load games:', error);
            gameListContainer.innerHTML = `<p style="text-align:center; grid-column: 1 / -1; color: #ff6b6b;">无法加载游戏列表，请刷新页面或联系管理员。</p>`;
        }
    }
    
    init();
}


// --- Logic for Registration Page (index.html) ---
function setupRegistrationPage() {
    const form = document.getElementById('register-form');
    const messageDiv = document.getElementById('form-message');
    const captchaBox = document.getElementById('captcha-box');

    // 生成随机验证码
    function generateCaptcha() {
        const captcha = Math.floor(1000 + Math.random() * 9000).toString();
        captchaBox.textContent = captcha;
        return captcha;
    }

    // 初始化验证码
    let currentCaptcha = generateCaptcha();

    // 点击验证码刷新
    captchaBox.addEventListener('click', () => {
        currentCaptcha = generateCaptcha();
    });

    form.addEventListener('submit', function(event) {
        event.preventDefault();
        messageDiv.textContent = '';

        const phone = document.getElementById('phone').value;
        const serial = document.getElementById('serial').value;
        const order = document.getElementById('order').value;
        const captcha = document.getElementById('captcha').value;

        if (!phone || !serial || !order || !captcha) {
            messageDiv.textContent = '所有字段均为必填项。';
            messageDiv.style.color = '#ff6b6b';
            return;
        }

        // 验证码检查
        if (captcha !== currentCaptcha) {
            messageDiv.textContent = '验证码错误，请重新输入。';
            messageDiv.style.color = '#ff6b6b';
            currentCaptcha = generateCaptcha(); // 刷新验证码
            return;
        }

        // Redirect to games page with parameters
        const queryParams = new URLSearchParams({ phone, serial, order });
        window.location.href = `games.html?${queryParams.toString()}`;
    });
}

// --- Background Animation ---
function loadBackground() {
    const starsContainer = document.querySelector('.stars-bg');
    if (!starsContainer) return;

    // 创建星星
    function createStar() {
        const star = document.createElement('div');
        star.className = 'star';

        // 随机位置和大小
        const size = Math.random() * 3 + 1; // 1-4px
        const left = Math.random() * 100; // 0-100%
        const animationDuration = Math.random() * 3 + 2; // 2-5秒

        star.style.width = size + 'px';
        star.style.height = size + 'px';
        star.style.left = left + '%';
        star.style.top = '100vh'; // 从底部开始
        star.style.animationDuration = animationDuration + 's';

        starsContainer.appendChild(star);

        // 动画结束后移除星星
        setTimeout(() => {
            if (star.parentNode) {
                star.parentNode.removeChild(star);
            }
        }, animationDuration * 1000);
    }

    // 创建流星
    function createMeteor() {
        const meteor = document.createElement('div');
        meteor.className = 'meteor';

        const left = Math.random() * 100;
        const animationDuration = Math.random() * 2 + 1; // 1-3秒

        meteor.style.left = left + '%';
        meteor.style.top = '-10px';
        meteor.style.animationDuration = animationDuration + 's';

        starsContainer.appendChild(meteor);

        setTimeout(() => {
            if (meteor.parentNode) {
                meteor.parentNode.removeChild(meteor);
            }
        }, animationDuration * 1000);
    }

    // 定期创建星星和流星
    setInterval(createStar, 200); // 每200ms创建一个星星
    setInterval(createMeteor, 3000); // 每3秒创建一个流星
}
