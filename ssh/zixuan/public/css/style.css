/* General Body & Background Styles */
html, body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Aria<PERSON>, sans-serif;
    line-height: 1.6;
    color: #c7d5e0;
    position: relative; /* Needed for the pseudo-element background */
    overflow-x: hidden; /* Prevent horizontal scrollbar from stars */
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw; /* 使用视口宽度确保完全覆盖 */
    height: 100vh; /* 使用视口高度确保完全覆盖 */
    z-index: -1;
    background-color: #171a21;
    pointer-events: none; /* Make sure it doesn't intercept clicks */
    transform: translateZ(0); /* 强制硬件加速，防止移动 */
}

/* Starry Background Container */
.stars-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: -1;
    pointer-events: none;
    transform: translateZ(0); /* 强制硬件加速，防止移动 */
}

/* Starry Background */
.star {
    position: absolute; /* 相对于 stars-bg 容器定位 */
    background-color: #fff;
    border-radius: 50%;
    box-shadow: 0 0 6px #fff;
    animation-name: twinkle;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
}

@keyframes twinkle {
    from { transform: translateY(0vh); }
    to { transform: translateY(-200px) scale(1.5); opacity: 0; }
}

/* 流星样式 */
.meteor {
    position: absolute; /* 相对于 stars-bg 容器定位 */
    width: 2px;
    height: 2px;
    background-color: #fff;
    border-radius: 50%;
    box-shadow: 0 0 10px #fff, 0 0 20px #66c0f4, 0 0 30px #66c0f4;
    animation-name: meteorFall;
    animation-timing-function: linear;
    animation-iteration-count: 1;
}

.meteor::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100px;
    height: 1px;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.8) 50%, rgba(102,192,244,0.6) 100%);
    transform: rotate(45deg);
    transform-origin: 0 0;
}

@keyframes meteorFall {
    from {
        transform: translateY(0) translateX(0);
        opacity: 1;
    }
    to {
        transform: translateY(100vh) translateX(50px);
        opacity: 0;
    }
}

/* Page-specific layout wrappers to fix scrolling and centering */
#page-games {
    padding: 40px 20px;
    box-sizing: border-box;
}

/* 登录页面布局 */
#page-index {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
    box-sizing: border-box;
}

/* Main Content Container */
.container {
    width: 90%;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: rgba(18, 24, 39, 0.85);
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.5);
    z-index: 10;
}

/* 登录页面容器样式 */
#page-index .container {
    width: 100%;
    max-width: 400px; /* 限制最大宽度，更紧凑 */
    margin: 0;
    padding: 30px;
    text-align: center; /* 所有内容居中 */
}

#page-index h1 {
    font-size: 2rem;
    margin-bottom: 0.5em;
    color: #66c0f4;
    text-shadow: 0 0 10px #66c0f4;
}

#page-index p {
    font-size: 0.9rem;
    margin-bottom: 1.5em;
    color: #a4b2bc;
}

/* Header & Title - THIS FIXES THE TITLE */
header {
    text-align: center;
    color: #c7d5e0;
    margin-bottom: 20px;
    border-bottom: 2px solid #2a475e;
    padding-bottom: 15px;
}

header h1 {
    color: #66c0f4;
    margin-bottom: 10px;
}

header p {
    color: #a4b2bc;
    font-size: 0.95rem;
    line-height: 1.5;
}

/* 登录页面表单样式 */
#page-index .form-group {
    margin-bottom: 18px;
    text-align: left; /* 表单项左对齐 */
}

#page-index .form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #c7d5e0;
    font-size: 0.9rem;
}

#page-index .form-group input[type="text"],
#page-index .form-group input[type="tel"] {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #2a475e;
    border-radius: 6px;
    box-sizing: border-box;
    background-color: rgba(0, 0, 0, 0.2);
    color: #c7d5e0;
    transition: border-color 0.2s, box-shadow 0.2s;
    font-size: 0.9rem;
}

/* 通用表单样式（用于游戏页面等） */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #c7d5e0;
}

.form-group input[type="text"],
.form-group input[type="tel"] {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #2a475e;
    border-radius: 4px;
    box-sizing: border-box;
    background-color: rgba(0, 0, 0, 0.2);
    color: #c7d5e0;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input::placeholder {
    color: #7a8b99;
}

.form-group input:focus {
    outline: none;
    border-color: #66c0f4;
    box-shadow: 0 0 0 3px rgba(102, 192, 244, 0.2);
}

/* 登录页面按钮样式 */
#page-index .btn {
    width: 100%;
    padding: 12px 20px;
    background-color: #66c0f4;
    color: #fff;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    font-size: 1rem;
    margin-top: 10px;
    transition: all 0.3s ease;
}

#page-index .btn:hover {
    background-color: #4a9fdf;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 192, 244, 0.3);
}

/* 通用按钮样式 */
.btn {
    padding: 10px 20px;
    background-color: #66c0f4;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.2s;
}

.btn:hover {
    background-color: #4a9fdf;
}

/* 登录页面验证码样式 */
#page-index .captcha-group {
    display: flex;
    align-items: flex-end;
    gap: 10px;
}

#page-index .captcha-group label {
    width: 100%;
    margin-bottom: 6px;
}

#page-index .captcha-group input {
    flex-grow: 1;
}

#page-index .captcha-box {
    padding: 10px 16px;
    background-color: rgba(0, 0, 0, 0.3);
    border: 1px solid #2a475e;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: bold;
    letter-spacing: 3px;
    cursor: pointer;
    user-select: none;
    color: #66c0f4;
    min-width: 80px;
    text-align: center;
    transition: background-color 0.2s;
}

#page-index .captcha-box:hover {
    background-color: rgba(102, 192, 244, 0.1);
}

/* 通用验证码样式 */
.captcha-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.captcha-group input {
    flex-grow: 1;
}

.captcha-box {
    padding: 10px 20px;
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid #2a475e;
    border-radius: 4px;
    font-size: 1.2em;
    font-weight: bold;
    letter-spacing: 5px;
    cursor: pointer;
    user-select: none;
    text-decoration: none;
    color: #c7d5e0;
}

/* 登录页面消息区域 */
#page-index .message-area {
    margin-top: 15px;
    font-weight: bold;
    min-height: 20px;
    font-size: 0.85rem;
    text-align: center;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

/* 通用消息区域 */
.message-area {
    margin-top: 15px;
    font-weight: bold;
    min-height: 20px;
}

/* Game Page Specifics - THIS FIXES SCROLLING */
#page-games .container {
    max-height: none;
    display: block;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 15px;
}

#search-bar {
    flex-grow: 1;
    padding: 10px;
    background-color: #1b2838;
    border: 1px solid #2a475e;
    color: #c7d5e0;
    border-radius: 4px;
}

.selection-counter {
    background-color: #1b2838;
    padding: 10px 15px;
    border-radius: 4px;
    white-space: nowrap;
}

#submit-selection {
    width: auto;
    min-width: 120px;
}

#game-selection-form {
    overflow-y: visible;
}

/* Game List Container styles */
#game-list-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
    min-height: 400px; 
}

.game-item {
    background-color: #1b2838;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #131a23;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    border-left: 5px solid transparent;
    overflow: hidden;
}

.game-item:hover {
    border-color: #66c0f4;
    transform: translateY(-2px);
}

.game-item.selected {
    background-color: #2a475e;
    border-left-color: #66c0f4;
}

.game-item input[type="checkbox"] {
    margin-right: 10px;
    width: 14px;
    height: 14px;
    flex-shrink: 0;
}

.game-item label {
    color: #c7d5e0;
    flex-grow: 1;
    word-break: break-word;
    min-width: 0;
}

/* Pagination Controls */
.pagination-controls {
    margin-top: 20px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    padding-bottom: 10px;
    font-size: 1.1em;
}

.pagination-controls button {
    background-color: #2a475e;
    color: #c7d5e0;
    border: 1px solid #1b2838;
    padding: 6px 12px; /* 减小内边距 */
    cursor: pointer;
    border-radius: 4px;
    font-size: 0.85em; /* 调小字体 */
    writing-mode: horizontal-tb; /* 确保横着写 */
}

.pagination-controls button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-controls button.active {
    background-color: #66c0f4;
    color: #fff;
}

.pagination-ellipsis {
    padding: 0 10px;
}

.page-jump-container {
    display: flex;
    align-items: center;
    margin-left: 15px;
}

.page-jump-container input {
    width: 60px;
    padding: 8px;
    text-align: center;
    background-color: #1b2838;
    border: 1px solid #2a475e;
    color: #c7d5e0;
    border-radius: 4px;
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
    /* 整体容器调整 */
    .container {
        width: 95%;
        margin: 10px auto;
        padding: 15px;
    }

    /* 页面布局调整 */
    #page-games {
        padding: 20px 10px;
    }

    /* 标题字体调整 */
    .container h1 {
        font-size: 1.8rem;
        margin-bottom: 0.3em;
    }

    .container p {
        font-size: 0.9rem;
        margin-bottom: 1em;
    }

    /* 内容布局调整 - 不再需要侧边栏 */
    .content-wrapper {
        display: block; /* 简化布局 */
    }

    /* 移动端不再需要侧边栏，使用购物车代替 */

    /* 工具栏调整 */
    .toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
        margin-bottom: 15px;
    }

    #search-bar {
        padding: 8px;
        font-size: 0.9rem;
    }

    .selection-counter {
        padding: 8px 12px;
        font-size: 0.85em;
        text-align: center;
    }

    /* 游戏列表调整 */
    #game-list-container {
        grid-template-columns: 1fr;
        gap: 10px;
        min-height: 300px;
    }

    .game-item {
        padding: 12px;
        font-size: 0.9rem;
    }

    .game-item input[type="checkbox"] {
        width: 16px;
        height: 16px;
    }

    /* 分页控制调整 */
    .pagination-controls {
        flex-wrap: wrap;
        gap: 3px;
        font-size: 0.9em;
        padding-bottom: 5px;
    }

    .pagination-controls button {
        padding: 5px 8px;
        font-size: 0.75em;
        min-width: 35px;
    }

    .page-jump-container {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
        justify-content: center;
    }

    .page-jump-container input {
        width: 50px;
        padding: 6px;
        font-size: 0.8rem;
    }

    /* 提交区域调整 */
    .submission-area {
        margin-top: 20px;
        padding: 15px;
    }

    .submission-area input {
        width: 200px;
        padding: 8px;
        margin-right: 5px;
        font-size: 0.9rem;
    }

    #submit-selection {
        min-width: 100px;
        padding: 8px 15px;
        font-size: 0.9rem;
    }
}

/* 更小屏幕的优化 (小于480px) */
@media (max-width: 480px) {
    /* 进一步缩小容器 */
    .container {
        width: 98%;
        margin: 5px auto;
        padding: 10px;
    }

    #page-games {
        padding: 15px 5px;
    }

    /* 标题进一步缩小 */
    .container h1 {
        font-size: 1.5rem;
    }

    .container p {
        font-size: 0.8rem;
    }

    /* 更小屏幕使用购物车，不需要侧边栏 */

    /* 工具栏进一步优化 */
    #search-bar {
        padding: 6px;
        font-size: 0.85rem;
    }

    .selection-counter {
        padding: 6px 10px;
        font-size: 0.8em;
    }

    /* 游戏项进一步优化 */
    .game-item {
        padding: 10px;
        font-size: 0.85rem;
    }

    /* 分页控制进一步优化 */
    .pagination-controls button {
        padding: 4px 6px;
        font-size: 0.7em;
        min-width: 30px;
    }

    .page-jump-container input {
        width: 45px;
        padding: 5px;
        font-size: 0.75rem;
    }

    /* 提交区域进一步优化 */
    .submission-area {
        padding: 10px;
    }

    .submission-area input {
        width: 180px;
        padding: 6px;
        font-size: 0.85rem;
    }

    #submit-selection {
        padding: 6px 12px;
        font-size: 0.85rem;
    }
}

/* 登录页面移动端优化 */
@media (max-width: 768px) {
    #page-index {
        padding: 15px 10px;
    }

    #page-index .container {
        max-width: 350px;
        padding: 25px 20px;
    }

    #page-index h1 {
        font-size: 1.6rem;
    }

    #page-index p {
        font-size: 0.85rem;
    }

    #page-index .captcha-group {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    #page-index .captcha-group label {
        margin-bottom: 6px;
    }

    #page-index .captcha-box {
        align-self: center;
        min-width: 100px;
    }
}

@media (max-width: 480px) {
    #page-index {
        padding: 10px 5px;
    }

    #page-index .container {
        max-width: 320px;
        padding: 20px 15px;
    }

    #page-index h1 {
        font-size: 1.4rem;
    }

    #page-index .form-group input[type="text"],
    #page-index .form-group input[type="tel"] {
        padding: 8px 10px;
        font-size: 0.85rem;
    }

    #page-index .captcha-box {
        padding: 8px 12px;
        font-size: 0.9rem;
        min-width: 90px;
    }
}

/* 通用移动端优化 */
@media (max-width: 768px) {
    /* 确保所有文本不会溢出 */
    * {
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    /* 防止水平滚动 */
    body {
        overflow-x: hidden;
    }

    /* 确保容器不会超出屏幕 */
    .container, .sidebar, .main-content {
        max-width: 100%;
        box-sizing: border-box;
    }

    /* 移除不必要的边距 */
    .content-wrapper {
        margin: 0;
    }

    /* 优化按钮在移动端的显示 */
    .remove-btn {
        font-size: 1em;
        padding: 2px 4px;
        min-width: 20px;
    }

    /* 优化消息区域 */
    .message-area {
        font-size: 0.85rem;
        word-break: break-word;
    }

    /* 移动端购物车优化 */
    .game-cart {
        top: 15px;
        right: 15px;
    }

    .cart-icon {
        width: 45px;
        height: 45px;
    }

    .cart-count {
        width: 18px;
        height: 18px;
        font-size: 0.65rem;
        top: -4px;
        right: -4px;
    }

    .cart-dropdown {
        width: 280px;
        top: 55px;
    }

    .cart-header {
        padding: 10px 14px;
        font-size: 0.85rem;
    }

    .cart-item-name {
        font-size: 0.75rem;
    }
}

@media (max-width: 480px) {
    /* 更小屏幕的购物车优化 */
    .game-cart {
        top: 10px;
        right: 10px;
    }

    .cart-icon {
        width: 40px;
        height: 40px;
    }

    .cart-count {
        width: 16px;
        height: 16px;
        font-size: 0.6rem;
        top: -3px;
        right: -3px;
    }

    .cart-dropdown {
        width: 260px;
        top: 50px;
        right: -10px; /* 调整位置避免超出屏幕 */
    }

    .cart-item-name {
        font-size: 0.7rem;
    }
}

.container h1 {
    font-size: 2.5rem;
    color: #c7d5e0;
    text-shadow: 0 0 10px #66c0f4, 0 0 20px #66c0f4;
    margin-bottom: 0.5em;
}

.container p {
    color: #c7d5e0;
    font-size: 1rem;
    max-width: 600px;
    margin: 0 auto 1.5em;
    line-height: 1.6;
}

/* New layout styles */
.content-wrapper {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.main-content {
    flex-grow: 1;
    min-width: 0;
}

.sidebar {
    width: 30%;
    max-width: 350px;
    background-color: #101822;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #2a475e;
    position: sticky;
    top: 20px;
    transition: all 0.3s ease;
}

/* 折叠状态的侧边栏 */
.sidebar.collapsed {
    position: fixed;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: auto;
    min-width: 120px;
    max-width: 200px;
    z-index: 100;
    box-shadow: 0 4px 20px rgba(0,0,0,0.7);
}

.sidebar h2 {
    color: #66c0f4;
    margin-top: 0;
    margin-bottom: 10px;
    border-bottom: 1px solid #2a475e;
    padding-bottom: 8px;
    font-size: 0.75em; /* 进一步调小"已选游戏"字体 */
}

#selected-games-list {
    list-style-type: decimal;
    padding-left: 20px;
    margin: 0;
    word-break: break-word;
}

#selected-games-list li {
    padding: 6px 10px;
    background-color: #1b2838;
    margin-bottom: 5px;
    border-radius: 4px;
    border-left: 3px solid #66c0f4;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8em;
}

.remove-btn {
    background: transparent;
    border: none;
    color: #ff6b6b;
    font-size: 1.2em;
    cursor: pointer;
    padding: 0 5px;
}

#sidebar-toggle {
    cursor: pointer;
    user-select: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.toggle-arrow {
    transition: transform 0.3s ease;
}

.sidebar.collapsed .toggle-arrow {
    transform: rotate(-90deg);
}

.collapsible-content {
    max-height: 60vh;
    overflow-y: auto;
    transition: max-height 0.4s ease-out, padding 0.3s ease;
}

.sidebar.collapsed .collapsible-content {
    max-height: 0;
    overflow: hidden;
}

/* 折叠状态下的标题样式 */
.sidebar.collapsed h2 {
    font-size: 0.7em;
    margin-bottom: 5px;
    padding-bottom: 5px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar.collapsed .toggle-arrow {
    font-size: 0.8em;
}

/* 购物车样式 */
.game-cart {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.cart-icon {
    background-color: #66c0f4;
    color: white;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(102, 192, 244, 0.3);
    transition: all 0.3s ease;
    position: relative;
}

.cart-icon:hover {
    background-color: #4a9fdf;
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(102, 192, 244, 0.4);
}

.cart-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #ff6b6b;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
    min-width: 20px;
}

.cart-count.hidden {
    display: none;
}

.cart-dropdown {
    position: absolute;
    top: 60px;
    right: 0;
    background-color: #1b2838;
    border: 1px solid #2a475e;
    border-radius: 8px;
    width: 300px;
    max-height: 400px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.6);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.cart-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.cart-header {
    padding: 12px 16px;
    border-bottom: 1px solid #2a475e;
    font-weight: bold;
    color: #66c0f4;
    font-size: 0.9rem;
}

.cart-items {
    max-height: 320px;
    overflow-y: auto;
    padding: 8px;
}

.empty-cart {
    padding: 20px;
    text-align: center;
    color: #a4b2bc;
    font-size: 0.85rem;
}

.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 4px;
    background-color: #101822;
    border-radius: 4px;
    border-left: 3px solid #66c0f4;
}

.cart-item-name {
    flex-grow: 1;
    font-size: 0.8rem;
    color: #c7d5e0;
    word-break: break-word;
    margin-right: 8px;
    line-height: 1.3;
}

.cart-item-remove {
    background: transparent;
    border: none;
    color: #ff6b6b;
    font-size: 1.1rem;
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.cart-item-remove:hover {
    background-color: rgba(255, 107, 107, 0.1);
}

.games-page-container {
    width: 95%;
    max-width: 1400px;
    margin: 20px auto;
    padding: 20px;
    background: rgba(18, 24, 39, 0.85);
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
    z-index: 10;
}

.games-layout {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

#selected-games-sidebar {
    width: 25%;
    max-width: 300px;
    background-color: #101822;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #2a475e;
    position: sticky;
    top: 20px;
    flex-shrink: 0;
}

.games-list-container {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    height: calc(90vh - 40px);
}

.breadcrumb-container {
    padding: 10px 15px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    margin-bottom: 15px;
    text-align: right;
    flex-shrink: 0;
}

.breadcrumb-container a {
    color: #66c0f4;
    text-decoration: none;
}

.breadcrumb-container a:hover {
    text-decoration: underline;
}

.breadcrumb-container span {
    color: #c7d5e0;
}

#search-bar-container {
    margin-bottom: 15px;
    flex-shrink: 0;
}

#games-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    overflow-y: auto;
    flex-grow: 1;
    padding-right: 10px;
}

#pagination {
    margin-top: auto;
    padding-top: 15px;
    flex-shrink: 0;
}

.submission-area {
    margin-top: 30px;
    padding: 20px;
    border-top: 2px solid #2a475e;
    text-align: center;
}

.submission-area p {
    margin-bottom: 10px;
}

.submission-area input {
    padding: 10px;
    width: 250px;
    margin-right: 10px;
    border-radius: 4px;
    border: 1px solid #2a475e;
    background-color: #1b2838;
    color: #c7d5e0;
}

/* 黑白科技感发光主题样式 */
:root {
  --tag-glow-color: #fff;      /* 主发光色：白色 */
  --tag-glow-color2: #222;     /* 辅助发光色：黑色/深灰 */
}

/* 霓虹科技按钮（黑白风格） */
button, .btn, input[type="submit"], input[type="button"], .button {
  background: linear-gradient(90deg, #fff 0%, #222 100%);
  color: #222 !important;
  border: 2px solid #fff;
  border-radius: 10px;
  box-shadow: 0 0 12px #fff, 0 0 24px #222;
  padding: 6px 18px;
  font-weight: 600;
  font-size: 0.95rem;
  letter-spacing: 1px;
  text-shadow: 0 0 6px #fff, 0 0 12px #222;
  transition: background 0.3s, box-shadow 0.3s, border-color 0.3s, transform 0.2s;
  outline: none !important;
  position: relative;
  overflow: hidden;
}
button:hover, .btn:hover, input[type="submit"]:hover, input[type="button"]:hover, .button:hover {
  background: linear-gradient(90deg, #222 0%, #fff 100%);
  border-color: #222;
  box-shadow: 0 0 24px #fff, 0 0 48px #222;
  color: #fff !important;
  transform: scale(1.04);
}

/* 按钮发光动画 */
@keyframes neon-glow {
  0%, 100% { box-shadow: 0 0 12px #fff, 0 0 24px #222; }
  50% { box-shadow: 0 0 24px #fff, 0 0 48px #222; }
}
button, .btn {
  animation: neon-glow 2.2s infinite alternate;
}

/* 分享、收藏等按钮专属缩小（可根据实际class调整） */
.share-btn, .fav-btn, .ripro-share, .ripro-fav {
  padding: 4px 12px !important;
  font-size: 0.85rem !important;
  border-radius: 8px !important;
  min-width: unset !important;
  min-height: unset !important;
}

/* 标签/徽章黑白发光 */
.tag, .tags a, .post-tag, .label, .badge {
  background: rgba(30,30,30,0.85);
  color: var(--tag-glow-color) !important;
  border: 1.5px solid var(--tag-glow-color);
  border-radius: 16px;
  box-shadow: 0 0 8px var(--tag-glow-color), 0 0 16px var(--tag-glow-color2);
  text-shadow: 0 0 6px var(--tag-glow-color);
  font-size: 0.85rem;
  padding: 2px 10px;
  transition: background 0.3s, box-shadow 0.3s, border-color 0.3s, color 0.3s;
  text-decoration: none !important;
  position: relative;
  overflow: hidden;
}
.tag:hover, .tags a:hover, .post-tag:hover, .label:hover, .badge:hover {
  background: linear-gradient(90deg, var(--tag-glow-color)33 0%, var(--tag-glow-color2)33 100%);
  color: #222 !important;
  border-color: var(--tag-glow-color2);
  box-shadow: 0 0 16px var(--tag-glow-color), 0 0 32px var(--tag-glow-color2);
}

/* 标签独立发光动画 */
@keyframes tag-neon-glow {
  0%, 100% { box-shadow: 0 0 8px var(--tag-glow-color), 0 0 16px var(--tag-glow-color2); }
  50% { box-shadow: 0 0 16px var(--tag-glow-color), 0 0 32px var(--tag-glow-color2); }
}
.tag, .tags a, .post-tag, .label, .badge {
  animation: tag-neon-glow 2.2s infinite alternate;
}