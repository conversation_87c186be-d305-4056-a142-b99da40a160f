:root {
  --tag-glow-color: #7fd6ff;      /* 主发光色：淡蓝 */
  --tag-glow-color2: #1a2a3a;     /* 辅助发光色：深蓝灰 */
  --tag-bg-dark: #10151b;         /* 深色背景 */
  --tag-border: #2a3a4a;          /* 深色边框 */
}

/* 通用按钮（含分享、收藏等） */
button, .btn, input[type="submit"], input[type="button"], .button,
.share-btn, .fav-btn, .ripro-share, .ripro-fav {
  background: linear-gradient(90deg, #18232e 0%, #22384a 100%);
  color: #bfeaff !important;
  border: 1.5px solid var(--tag-glow-color);
  border-radius: 8px;
  box-shadow: 0 0 8px #7fd6ff44, 0 0 16px #1a2a3a33;
  padding: 5px 16px;
  font-weight: 500;
  font-size: 0.92rem;
  letter-spacing: 1px;
  text-shadow: 0 0 4px #7fd6ff88;
  transition: background 0.3s, box-shadow 0.3s, border-color 0.3s, color 0.3s, transform 0.2s;
  outline: none !important;
  position: relative;
  overflow: hidden;
}
button:hover, .btn:hover, input[type="submit"]:hover, input[type="button"]:hover, .button:hover,
.share-btn:hover, .fav-btn:hover, .ripro-share:hover, .ripro-fav:hover {
  background: linear-gradient(90deg, #22384a 0%, #18232e 100%);
  color: #fff !important;
  border-color: #7fd6ff;
  box-shadow: 0 0 16px #7fd6ff, 0 0 32px #1a2a3a;
  transform: scale(1.04);
}

/* 按钮发光动画 */
@keyframes neon-glow {
  0%, 100% { box-shadow: 0 0 8px #7fd6ff44, 0 0 16px #1a2a3a33; }
  50% { box-shadow: 0 0 16px #7fd6ff, 0 0 32px #1a2a3a; }
}
button, .btn, .share-btn, .fav-btn, .ripro-share, .ripro-fav {
  animation: neon-glow 2.2s infinite alternate;
}

/* 标签/徽章 */
.tag, .tags a, .post-tag, .label, .badge {
  background: var(--tag-bg-dark);
  color: var(--tag-glow-color) !important;
  border: 1.5px solid var(--tag-glow-color);
  border-radius: 14px;
  box-shadow: 0 0 6px var(--tag-glow-color), 0 0 12px var(--tag-glow-color2);
  text-shadow: 0 0 4px var(--tag-glow-color);
  font-size: 0.85rem;
  padding: 2px 10px;
  transition: background 0.3s, box-shadow 0.3s, border-color 0.3s, color 0.3s;
  text-decoration: none !important;
  position: relative;
  overflow: hidden;
}
.tag:hover, .tags a:hover, .post-tag:hover, .label:hover, .badge:hover {
  background: linear-gradient(90deg, var(--tag-glow-color)22 0%, var(--tag-glow-color2)33 100%);
  color: #fff !important;
  border-color: var(--tag-glow-color2);
  box-shadow: 0 0 12px var(--tag-glow-color), 0 0 24px var(--tag-glow-color2);
}

@keyframes tag-neon-glow {
  0%, 100% { box-shadow: 0 0 6px var(--tag-glow-color), 0 0 12px var(--tag-glow-color2); }
  50% { box-shadow: 0 0 12px var(--tag-glow-color), 0 0 24px var(--tag-glow-color2); }
}
.tag, .tags a, .post-tag, .label, .badge {
  animation: tag-neon-glow 2.2s infinite alternate;
}

/* 搜索栏、输入框科技感 */
input[type="text"], input[type="search"], input[type="email"], input[type="password"], textarea {
  border: 1.5px solid var(--tag-glow-color) !important;
  border-radius: 8px !important;
  background: var(--tag-bg-dark);
  color: #bfeaff;
  box-shadow: 0 0 8px #7fd6ff22;
  padding: 7px 12px;
  font-size: 0.97rem;
  transition: border-color 0.3s, box-shadow 0.3s;
}
input[type="text"]:focus, input[type="search"]:focus, input[type="email"]:focus, input[type="password"]:focus, textarea:focus {
  border-color: #7fd6ff !important;
  box-shadow: 0 0 16px #7fd6ff88;
  outline: none;
} 